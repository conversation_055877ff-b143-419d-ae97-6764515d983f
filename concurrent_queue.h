#ifndef CONCURRENT_QUEUE_H
#define CONCURRENT_QUEUE_H

#include <pthread.h> // For pthread_mutex_t and pthread_cond_t
#include <stddef.h>  // For size_t
#include <stdint.h>  // For uint8_t

// Structure for an element in the queue
typedef struct {
  uint8_t *buffer;
  size_t len;
} queue_element_t;

// Structure for the concurrent queue
typedef struct {
  queue_element_t *elements; // Array of queue elements
  size_t capacity;           // Maximum number of elements
  size_t count;              // Current number of elements
  size_t head;               // Index of the front element
  size_t tail;               // Index of the next free slot
  pthread_mutex_t mutex;     // Mutex for thread safety
  pthread_cond_t not_empty;  // Condition variable for when queue is not empty
  pthread_cond_t not_full;   // Condition variable for when queue is not full
} queue_t;

/**
 * @brief Creates a new concurrent queue.
 *
 * @param capacity The maximum number of elements the queue can hold.
 * @return A pointer to the newly created queue, or NULL on failure.
 */
queue_t *queue_create(size_t capacity);

/**
 * @brief Destroys a concurrent queue and frees its resources.
 *
 * This function will also free all buffers currently stored in the queue.
 *
 * @param q A pointer to the queue to be destroyed.
 */
void queue_destroy(queue_t *q);

/**
 * @brief Enqueues an element into the concurrent queue.
 *
 * This function makes a copy of the provided buffer.
 *
 * @param q A pointer to the queue.
 * @param buffer A pointer to the buffer to be enqueued.
 * @param len The length of the buffer.
 * @return 0 on success, -1 on failure (e.g., memory allocation error or queue
 * is NULL).
 */
int queue_enqueue(queue_t *q, const uint8_t *buffer, size_t len);

/**
 * @brief Dequeues an element from the concurrent queue.
 *
 * The caller is responsible for freeing the dequeued buffer using free().
 *
 * @param q A pointer to the queue.
 * @param buffer A pointer to a uint8_t* that will be set to the dequeued
 * buffer.
 * @param len A pointer to a size_t that will be set to the length of the
 * dequeued buffer.
 * @return 0 on success, -1 if the queue is NULL or if an error occurs.
 */
int queue_dequeue(queue_t *q, uint8_t **buffer, size_t *len);

#endif // CONCURRENT_QUEUE_H

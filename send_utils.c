#define _GNU_SOURCE
#include "send_utils.h"
#include "concurrent_queue.h"
#include "config_loader.h"
#include "hex_utils.h"
#include "include/sitp_lib.h"
#include <arpa/inet.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <pthread.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static queue_t *sitp_send_queue = NULL;
static pthread_t sender_thread_id;
static volatile int keep_sender_thread_running = 1;

static void *sitp_sender_thread_func(void *arg);

static void *sitp_sender_thread_func(void *arg) {
  (void)arg; // Unused argument

  printf("SITP sender thread started.\n");
  while (keep_sender_thread_running) {
    uint8_t *packet_buffer = NULL;
    size_t packet_len = 0;

    // Dequeue blocks until an item is available or queue is destroyed (if
    // modified to support that)
    if (queue_dequeue(sitp_send_queue, &packet_buffer, &packet_len) == 0) {
      if (packet_buffer != NULL && packet_len > 0) {
        // Check if this is a stop signal (single byte with value 0xFF)
        if (packet_len == 1 && packet_buffer[0] == 0xFF) {
          printf("Sender thread: Received stop signal.\n");
          free(packet_buffer);
          break;
        }

        int send_result = sitp_lib_send(packet_buffer, packet_len);
        if (send_result != 0) {
          fprintf(stderr,
                  "sitp_sender_thread_func: sitp_lib_send reported error "
                  "(code: %d) for packet of size %zu.\n",
                  send_result, packet_len);
        } else {
          // Packet sent successfully, now apply delay if configured
          const task_config_t *config = get_global_task_config();
          if (config && config->send_delay > 0) {
            usleep(config->send_delay);
          }
        }
        free(packet_buffer); // Free the buffer as per queue_dequeue contract
      } else if (!keep_sender_thread_running) {
        // Fallback: check the running flag if we get unexpected NULL
        printf("Sender thread: Received shutdown command.\n");
        if (packet_buffer != NULL) {
          free(packet_buffer);
        }
        break;
      }
      // If packet_buffer is NULL but keep_sender_thread_running is true, it
      // implies an issue or a specific signal from queue_dequeue not covered
      // yet.
    } else {
      // This case implies an error with queue_dequeue itself, or it was
      // unblocked for shutdown.
      if (!keep_sender_thread_running) {
        printf("Sender thread: Exiting due to shutdown signal (dequeue "
               "returned error or was unblocked).\n");
        break;
      }
      // Potentially add a small sleep here if queue_dequeue can return error
      // spuriously and not block, though typically it should block.
      fprintf(stderr, "sitp_sender_thread_func: queue_dequeue failed or was "
                      "interrupted. Retrying if running.\n");
    }
  }
  printf("SITP sender thread stopped.\n");
  return NULL;
}

int init_sitp_sender(size_t queue_capacity) {
  printf("Initializing SITP sender with queue capacity %zu...\n",
         queue_capacity);
  if (sitp_send_queue != NULL) {
    fprintf(stderr, "init_sitp_sender: SITP sender already initialized.\n");
    return -1;
  }

  sitp_send_queue = queue_create(queue_capacity);
  if (sitp_send_queue == NULL) {
    fprintf(stderr, "init_sitp_sender: Failed to create SITP send queue.\n");
    return -1;
  }

  keep_sender_thread_running = 1;
  if (pthread_create(&sender_thread_id, NULL, sitp_sender_thread_func, NULL) !=
      0) {
    fprintf(stderr, "init_sitp_sender: Failed to create SITP sender thread.\n");
    queue_destroy(sitp_send_queue);
    sitp_send_queue = NULL;
    return -1;
  }
  printf("SITP sender initialized successfully.\n");
  return 0;
}

void cleanup_sitp_sender(void) {
  printf("Cleaning up SITP sender...\n");
  if (sitp_send_queue == NULL && !keep_sender_thread_running) {
    printf("SITP sender already cleaned up or not initialized.\n");
    return;
  }

  if (keep_sender_thread_running) {
    keep_sender_thread_running = 0;

    printf("Signaling sender thread to stop...\n");

    // Send a "poison pill" (special marker) to wake up the blocked dequeue
    // operation
    if (sitp_send_queue != NULL) {
      // Enqueue a special stop signal packet (single byte with value 0xFF)
      // This will wake up the thread from pthread_cond_wait in queue_dequeue
      uint8_t stop_signal = 0xFF;
      if (queue_enqueue(sitp_send_queue, &stop_signal, 1) != 0) {
        fprintf(stderr, "cleanup_sitp_sender: Failed to enqueue stop signal. "
                        "Thread might be stuck.\n");
      }
    }

    if (pthread_join(sender_thread_id, NULL) != 0) {
      fprintf(stderr, "cleanup_sitp_sender: Failed to join SITP sender thread. "
                      "It might be stuck.\n");
      // Consider pthread_cancel as a last resort, but it's generally unsafe.
    } else {
      printf("SITP sender thread joined successfully.\n");
    }
  }

  if (sitp_send_queue != NULL) {
    queue_destroy(sitp_send_queue);
    sitp_send_queue = NULL;
    printf("SITP send queue destroyed.\n");
  }
  printf("SITP sender cleanup complete.\n");
}

int process_and_send_packet(uint8_t *buffer, size_t len) {
  printf("--- Queue SITP Packet Details ---\n");
  print_payload(buffer, len);
  printf("----------------------------\n");

  const task_config_t *config = get_global_task_config();
  if (config == NULL) {
    fprintf(stderr, "process_and_send_packet: Global config not loaded.\n");
    return -1;
  }

  if (sitp_send_queue == NULL) {
    fprintf(stderr, "process_and_send_packet: SITP send queue is not "
                    "initialized. Dropping packet.\n");
    return -1;
  }

  uint8_t *buffer_copy = (uint8_t *)malloc(len);
  if (buffer_copy == NULL) {
    fprintf(stderr,
            "process_and_send_packet: Failed to allocate memory for buffer "
            "copy (size: %zu).\n",
            len);
    return -1;
  }

  memcpy(buffer_copy, buffer, len);

  int enqueue_result = queue_enqueue(sitp_send_queue, buffer_copy, len);

  if (enqueue_result != 0) {
    fprintf(stderr,
            "process_and_send_packet: Failed to enqueue packet (size: %zu). "
            "Dropping packet.\n",
            len);
    // The buffer_copy was not successfully enqueued, so we must free it here
    free(buffer_copy);
    return -1;
  }

  return 0;
}

#ifndef IP_MAP_H
#define IP_MAP_H

#include <stdint.h>
#include "ut/uthash.h"

/**
 * IP mapping entry structure
 * Use uthash to store src_ip to dst_ip mapping relationships
 */
typedef struct ip_mapping {
    char src_ip[16];        // Source IP address string (max 15 chars + '\0')
    char dst_ip[16];        // Destination IP address string (max 15 chars + '\0')
    UT_hash_handle hh;      // uthash handle, must be named hh
} ip_mapping_t;

/**
 * Initialize IP mapping module
 * Load IP mapping relationships from specified file
 *
 * @param filename IP mapping file path
 * @return 0 indicates success, non-zero indicates failure
 */
int init_ip_map(const char *filename);

/**
 * Find destination IP based on source IP
 *
 * @param src_ip Source IP address string
 * @return Destination IP address string, returns NULL if not found
 */
const char *get_dst_ip(const char *src_ip);

/**
 * Clean up IP mapping module
 * Free all allocated memory
 */
void cleanup_ip_map(void);

/**
 * Add a new IP mapping pair to both the memory table and the file
 *
 * @param src_ip Source IP address string
 * @param dst_ip Destination IP address string
 * @return 0 indicates success, non-zero indicates failure
 */
int add_ip_mapping(const char *src_ip, const char *dst_ip);

/**
 * Get the number of entries in IP mapping table
 *
 * @return Number of mapping entries
 */
unsigned int get_ip_map_count(void);

/**
 * Print all IP mapping relationships (for debugging)
 */
void print_ip_mappings(void);

/**
 * Get all unique source IP addresses (keys) from the mapping table
 *
 * @param keys Pointer to store the array of unique source IPs (caller must free)
 * @param count Pointer to store the number of unique source IPs
 * @return 0 indicates success, non-zero indicates failure
 */
int get_all_src_ips(char ***keys, unsigned int *count);

/**
 * Get all unique destination IP addresses (values) from the mapping table
 *
 * @param values Pointer to store the array of unique destination IPs (caller must free)
 * @param count Pointer to store the number of unique destination IPs
 * @return 0 indicates success, non-zero indicates failure
 */
int get_all_dst_ips(char ***values, unsigned int *count);

/**
 * Free the memory allocated by get_all_src_ips or get_all_dst_ips
 *
 * @param array The array returned by get_all_src_ips or get_all_dst_ips
 * @param count The count of items in the array
 */
void free_ip_array(char **array, unsigned int count);

#endif // IP_MAP_H

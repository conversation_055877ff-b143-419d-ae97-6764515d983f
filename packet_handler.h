#ifndef PACKET_HANDLER_H
#define PACKET_HANDLER_H

#include <stddef.h>
#include <stdint.h>

/**
 * Callback function for handling received raw packets from SITP library.
 *
 * @param arg User-defined argument passed from sitp_lib_start
 * @param buffer Pointer to the received data buffer
 * @param len Length of the received data
 */
void raw_packet_receive_callback(void *arg, uint8_t *buffer, size_t len);

/**
 * Initialize the packet receiver with a queue of specified capacity.
 *
 * @param queue_capacity The maximum number of packets the queue can hold
 * @return 0 on success, non-zero on failure
 */
int init_packet_receiver(size_t queue_capacity);

/**
 * Clean up resources used by the packet receiver.
 */
void cleanup_packet_receiver(void);

/**
 * Encapsulates raw packet data with message header according to message_header_t structure.
 *
 * @param data Pointer to the original data buffer
 * @param data_len Length of the original data
 * @param msg_type Message type (use MSG_TYPE_* constants)
 * @param encap_len Pointer to store the length of the encapsulated data
 * @return Pointer to the newly allocated encapsulated data buffer (must be freed by caller)
 */
uint8_t *encapsulate_packet_with_type(const uint8_t *data, size_t data_len,
                                      int msg_type, size_t *encap_len);

/**
 * Encapsulates raw packet data with DATA_SYNC message type (backward compatibility).
 *
 * @param data Pointer to the original data buffer
 * @param data_len Length of the original data
 * @param encap_len Pointer to store the length of the encapsulated data
 * @return Pointer to the newly allocated encapsulated data buffer (must be freed by caller)
 */
uint8_t *encapsulate_packet(const uint8_t *data, size_t data_len,
                            size_t *encap_len);

#endif // PACKET_HANDLER_H

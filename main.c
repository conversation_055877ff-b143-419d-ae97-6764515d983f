#include <event2/buffer.h>
#include <event2/bufferevent.h>
#include <event2/listener.h>

#include <arpa/inet.h>
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>

#include "background_task.h"
#include "config_loader.h"
#include "daemonize.h"
#include "ip_map.h"
#include "log.h"
#include "packet_handler.h"
#include "packet_receiver.h"
#include "send_utils.h"

#define MAX_BUFFER_SIZE 65535

// Global variables for signal handler cleanup
static struct event_base *g_event_base = NULL;
static struct evconnlistener *g_listener = NULL;
static volatile sig_atomic_t g_shutdown_flag = 0;

static void read_cb(struct bufferevent *bev, void *ctx);
static void event_cb(struct bufferevent *bev, short events, void *ctx);
static void accept_conn_cb(struct evconnlistener *listener, evutil_socket_t fd,
                           struct sockaddr *address, int socklen, void *ctx);
static void accept_error_cb(struct evconnlistener *listener, void *ctx);

// Forward declarations for helper functions
static int initialize_resources(const server_config_t **server_conf_ptr,
                                const task_config_t **task_conf_ptr);
static struct event_base *
setup_event_loop(const server_config_t *server_conf,
                 struct evconnlistener **listener_ptr);
static void cleanup_resources(void);
static void init_logging(void);

// Forward declaration for cleanup function
static void cleanup_all_resources(struct event_base *base,
                                  struct evconnlistener *listener);

// External cleanup function that can be called from daemonize.c
void daemon_cleanup(void) {
  log_info("Starting daemon cleanup process");

  // Set shutdown flag to stop any running loops
  g_shutdown_flag = 1;

  // Cleanup global handles if available
  cleanup_all_resources(g_event_base, g_listener);

  log_info("Daemon cleanup process completed");
}

// Signal handler for graceful shutdown
static void signal_handler(int sig) {
  log_warn("Received signal %d, initiating graceful shutdown", sig);
  g_shutdown_flag = 1;

  // Break the event loop if it's running
  if (g_event_base) {
    event_base_loopexit(g_event_base, NULL);
  }

  // Perform immediate cleanup
  cleanup_all_resources(g_event_base, g_listener);

  // Exit the program
  exit(0);
}

// Function to setup signal handlers
static void setup_signal_handlers(void) {
  struct sigaction sa;
  sa.sa_handler = signal_handler;
  sigemptyset(&sa.sa_mask);
  sa.sa_flags = 0;

  // Handle SIGINT (Ctrl-C)
  if (sigaction(SIGINT, &sa, NULL) == -1) {
    log_error("Failed to set SIGINT handler: %s", strerror(errno));
  }

  // Handle SIGTERM (termination signal)
  if (sigaction(SIGTERM, &sa, NULL) == -1) {
    log_error("Failed to set SIGTERM handler: %s", strerror(errno));
  }

  // Handle SIGQUIT (quit signal)
  if (sigaction(SIGQUIT, &sa, NULL) == -1) {
    log_error("Failed to set SIGQUIT handler: %s", strerror(errno));
  }
}

static void read_cb(struct bufferevent *bev, void *ctx) {
  packet_receiver_context_t *receiver_ctx = (packet_receiver_context_t *)ctx;

  if (!receiver_ctx) {
    log_error("read_cb: No packet receiver context provided");
    bufferevent_free(bev);
    return;
  }

  int result = packet_receiver_process_data(bev, receiver_ctx);
  if (result != 0) {
    log_error("read_cb: packet_receiver_process_data failed with code %d", result);
    bufferevent_free(bev);
  }
}

static void event_cb(struct bufferevent *bev, short events, void *ctx) {
  packet_receiver_context_t *receiver_ctx = (packet_receiver_context_t *)ctx;

  if (events & BEV_EVENT_ERROR) {
    log_error("Error from bufferevent: %s", evutil_socket_error_to_string(EVUTIL_SOCKET_ERROR()));
  }
  if (events & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {
    log_info("Client disconnected or error occurred, cleaning up connection");

    if (receiver_ctx) {
      packet_receiver_cleanup(receiver_ctx);
    }

    bufferevent_free(bev);
  }
}

static void accept_conn_cb(struct evconnlistener *listener, evutil_socket_t fd,
                           struct sockaddr *address, int socklen, void *ctx) {
  struct event_base *base = evconnlistener_get_base(listener);
  struct bufferevent *bev;
  char client_ip[INET_ADDRSTRLEN];

  struct sockaddr_in *client_addr = (struct sockaddr_in *)address;
  inet_ntop(AF_INET, &(client_addr->sin_addr), client_ip, INET_ADDRSTRLEN);
  log_info("Accepted connection from %s:%d", client_ip, ntohs(client_addr->sin_port));

  bev = bufferevent_socket_new(base, fd, BEV_OPT_CLOSE_ON_FREE);
  if (!bev) {
    log_error("Error constructing bufferevent");
    event_base_loopbreak(base);
    return;
  }

  packet_receiver_context_t *receiver_ctx = packet_receiver_init();
  if (!receiver_ctx) {
    log_error("Error initializing packet receiver context");
    bufferevent_free(bev);
    return;
  }

  bufferevent_setcb(bev, read_cb, NULL, event_cb, receiver_ctx);
  bufferevent_enable(bev, EV_READ | EV_WRITE);
}

static void accept_error_cb(struct evconnlistener *listener, void *ctx) {
  struct event_base *base = evconnlistener_get_base(listener);
  int err = EVUTIL_SOCKET_ERROR();
  log_error("Got an error %d (%s) on the listener, shutting down",
           err, evutil_socket_error_to_string(err));

  event_base_loopexit(base, NULL);
}

static void init_logging(void) {
  const server_config_t *server_conf = get_global_server_config();

  if (server_conf->log_to_file) {
    const char *log_filename = server_conf->log_filename;
    FILE *fp = fopen(log_filename, "a");
    if (fp) {
      log_add_fp(fp, server_conf->log_level);
      log_info("Logging to file is enabled: %s", log_filename);
    } else {
      log_error("Failed to open log file '%s'", log_filename);
    }
  } else {
    log_info("Logging to file is disabled in the configuration.");
  }
}

static int initialize_resources(const server_config_t **server_conf_ptr,
                                const task_config_t **task_conf_ptr) {
  initialize_global_server_config("config.ini");
  initialize_global_task_config("config.ini");
  init_logging();

  *server_conf_ptr = get_global_server_config();
  *task_conf_ptr = get_global_task_config();

  log_info("Initializing IP mapping");
  if (init_ip_map("ip_mapping.txt") != 0) {
    log_warn("Failed to initialize IP mapping, continuing without IP mapping");
    // Consider if this should be a fatal error
  } else {
    log_info("IP mapping initialized successfully, total mappings: %u", get_ip_map_count());
    print_ip_mappings();
  }

  if (init_sitp_sender((*task_conf_ptr)->sitp_q_cap) != 0) {
    log_warn("Failed to initialize SITP sender, continuing with other initialization");
    // Consider if this should be a fatal error
  }

  init_background_task();

  log_info("Initializing Packet Handler's packet receiver with queue capacity %d",
           (*task_conf_ptr)->sitp_q_cap);
  if (init_packet_receiver((size_t)(*task_conf_ptr)->sitp_q_cap) != 0) {
    log_error("Failed to initialize Packet Handler's packet receiver");
    return -1; // Or handle error appropriately
  }
  log_info("Packet Handler's packet receiver initialized successfully");
  return 0;
}

static struct event_base *
setup_event_loop(const server_config_t *server_conf,
                 struct evconnlistener **listener_ptr) {
  struct event_base *base;
  struct sockaddr_in sin;

  base = event_base_new();
  if (!base) {
    log_error("Could not initialize libevent");
    return NULL;
  }

  memset(&sin, 0, sizeof(sin));
  sin.sin_family = AF_INET;

  // Use configured listen IP instead of listening on all interfaces
  if (inet_aton(server_conf->listen_ip, &sin.sin_addr) == 0) {
    log_error("Invalid listen IP address: %s", server_conf->listen_ip);
    event_base_free(base);
    return NULL;
  }

  sin.sin_port = htons(server_conf->listen_port);

  *listener_ptr = evconnlistener_new_bind(
      base, accept_conn_cb, NULL, LEV_OPT_CLOSE_ON_FREE | LEV_OPT_REUSEABLE, -1,
      (struct sockaddr *)&sin, sizeof(sin));

  if (!*listener_ptr) {
    log_error("Could not create a listener on port %u", server_conf->listen_port);
    event_base_free(base);
    return NULL;
  }
  evconnlistener_set_error_cb(*listener_ptr, accept_error_cb);
  log_info("Listening on %s:%u", server_conf->listen_ip, server_conf->listen_port);
  return base;
}

static void cleanup_resources() {
  log_info("Cleaning up Packet Handler's packet receiver");
  cleanup_packet_receiver();
  log_debug("Packet Handler's packet receiver cleaned up");

  log_info("Cleaning up SITP sender");
  cleanup_sitp_sender();
  log_debug("SITP sender cleaned up");

  log_info("Cleaning up IP mapping");
  cleanup_ip_map();
  log_debug("IP mapping cleaned up");
}

static void cleanup_all_resources(struct event_base *base,
                                  struct evconnlistener *listener) {
  log_info("Starting cleanup process");

  if (listener) {
    log_debug("Freeing listener");
    evconnlistener_free(listener);
  }

  if (base) {
    log_debug("Freeing event base");
    event_base_free(base);
  }

  // Clean up other resources using existing function
  cleanup_resources();

  log_info("Cleanup process completed");
}

int daemon_start() {
  const server_config_t *server_conf = NULL;
  const task_config_t *task_conf = NULL;
  struct event_base *base = NULL;
  struct evconnlistener *listener = NULL;

  if (initialize_resources(&server_conf, &task_conf) != 0) {
    log_error("Failed to initialize resources, exiting");
    return 1;
  }

  base = setup_event_loop(server_conf, &listener);
  if (!base || !listener) {
    log_error("Failed to setup event loop, exiting");
    if (base)
      event_base_free(base);
    cleanup_resources(); // Clean up what was initialized
    return 1;
  }

  // Store handles in global variables for signal handler access
  g_event_base = base;
  g_listener = listener;

  // Check if we're running as a daemon (stdout redirected to /dev/null)
  // If not, setup our own signal handlers
  struct stat stat_buf;
  if (fstat(STDOUT_FILENO, &stat_buf) == 0) {
    // Check if stdout is /dev/null (typical for daemon mode)
    struct stat null_stat;
    if (stat("/dev/null", &null_stat) == 0 &&
        stat_buf.st_dev == null_stat.st_dev &&
        stat_buf.st_ino == null_stat.st_ino) {
      log_info("Running in daemon mode, using daemonize.c signal handlers");
    } else {
      // Setup signal handlers for graceful shutdown (non-daemon mode)
      log_info("Setting up signal handlers for non-daemon mode");
      setup_signal_handlers();
    }
  } else {
    // If we can't determine, setup signal handlers anyway
    log_info("Setting up signal handlers");
    setup_signal_handlers();
  }

  event_base_dispatch(base); // Blocking call

  // Cleanup after event_base_dispatch returns (e.g., on loopexit or error)
  cleanup_all_resources(base, listener);

  // Clear global variables
  g_event_base = NULL;
  g_listener = NULL;

  log_info("Server shut down");
  return 0;
}

int main(int argc, const char **argv) { return daemon_main(argc, argv); }

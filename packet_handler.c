#include "packet_handler.h"
#include "concurrent_queue.h"
#include "config_loader.h"
#include "hex_utils.h"
#include "ip_map.h"
#include "message_common.h"
#include "tcp_client.h"
#include <arpa/inet.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static queue_t *packet_receive_queue = NULL;
static pthread_t receiver_thread_id;
static volatile int keep_receiver_thread_running = 1;

/**
 * Validates whether a string is a valid IPv4 address.
 *
 * @param ip_str The IP address string to validate
 * @return 1 if valid IPv4 address, 0 otherwise
 */
static int is_valid_ipv4(const char *ip_str) {
  struct sockaddr_in sa;
  int result = inet_pton(AF_INET, ip_str, &(sa.sin_addr));
  return result != 0;
}

/**
 * Encapsulates raw packet data with message header according to
 * message_header_t structure.
 *
 * @param data Pointer to the original data buffer
 * @param data_len Length of the original data
 * @param msg_type Message type (use MSG_TYPE_* constants)
 * @param encap_len Pointer to store the length of the encapsulated data
 * @return Pointer to the newly allocated encapsulated data buffer (must be
 * freed by caller)
 */
uint8_t *encapsulate_packet_with_type(const uint8_t *data, size_t data_len,
                                      int msg_type, size_t *encap_len) {
  const size_t header_size = sizeof(message_header_t);
  *encap_len = header_size + data_len;

  uint8_t *encap_data = (uint8_t *)malloc(*encap_len);
  if (encap_data == NULL) {
    fprintf(stderr, "Failed to allocate memory for encapsulated packet\n");
    return NULL;
  }

  message_header_t *header = (message_header_t *)encap_data;

  memcpy(header->magic, MAGIC_NUMBER, MAGIC_LEN);

  header->len = (int)data_len;

  header->type = msg_type;

  memcpy(encap_data + header_size, data, data_len);

  return encap_data;
}

/**
 * Encapsulates raw packet data with DATA_SYNC message type (backward
 * compatibility).
 *
 * @param data Pointer to the original data buffer
 * @param data_len Length of the original data
 * @param encap_len Pointer to store the length of the encapsulated data
 * @return Pointer to the newly allocated encapsulated data buffer (must be
 * freed by caller)
 */
uint8_t *encapsulate_packet(const uint8_t *data, size_t data_len,
                            size_t *encap_len) {
  return encapsulate_packet_with_type(data, data_len, MSG_TYPE_DATA_SYNC,
                                      encap_len);
}

/**
 * Validates and trims packet data according to configuration.
 *
 * @param buffer Original packet buffer
 * @param len Original packet length
 * @param trimmed_buffer Output pointer to trimmed buffer
 * @param trimmed_len Output pointer to trimmed length
 * @return 0 if successfully trimmed, -1 if cannot trim
 */
static int validate_and_trim_packet(uint8_t *buffer, size_t len,
                                    uint8_t **trimmed_buffer,
                                    size_t *trimmed_len) {
  const task_config_t *config = get_global_task_config();

  if (len <= config->pkt_trim_bytes) {
    printf("Packet too small (%zu bytes), cannot trim %u bytes.\n", len,
           config->pkt_trim_bytes);
    return -1;
  }

  *trimmed_buffer = buffer + config->pkt_trim_bytes;
  *trimmed_len = len - config->pkt_trim_bytes;

  return 0;
}

/**
 * Validates message header structure.
 *
 * @param buffer Trimmed packet buffer
 * @param len Trimmed packet length
 * @param header Output pointer to header structure
 * @param payload_len Output pointer to payload length
 * @param msg_type Output pointer to message type
 * @return 0 if valid header, -1 otherwise
 */
static int validate_message_header(uint8_t *buffer, size_t len,
                                   message_header_t **header, int *payload_len,
                                   int *msg_type) {
  if (len < sizeof(message_header_t)) {
    printf("Trimmed packet too small (%zu bytes) to contain message header.\n",
           len);
    return -1;
  }

  *header = (message_header_t *)buffer;

  if (memcmp((*header)->magic, MAGIC_NUMBER, MAGIC_LEN) != 0) {
    printf("Magic number mismatch.\n");
    return -1;
  }

  *payload_len = (*header)->len;
  *msg_type = (*header)->type;

  printf("Found valid message header: type=%d, payload_len=%d\n", *msg_type,
         *payload_len);

  if (*msg_type != MSG_TYPE_CONFIG) {
    printf("Message type is not CONFIG (%d).\n", *msg_type);
    return -1;
  }

  if (*payload_len < 0 || *payload_len > MAX_CONFIG_PAYLOAD_SIZE) {
    printf("Invalid payload length (%d).\n", *payload_len);
    return -1;
  }

  size_t total_msg_len = sizeof(message_header_t) + *payload_len;
  if (len < total_msg_len) {
    printf("Incomplete message (have %zu bytes, need %zu).\n", len,
           total_msg_len);
    return -1;
  }

  return 0;
}

/**
 * Processes configuration message and updates target IP if valid.
 *
 * @param buffer Trimmed packet buffer
 * @param payload_len Length of the payload
 * @return 0 if config updated successfully, -1 if should process normally
 */
static int process_config_message(uint8_t *buffer, int payload_len) {
  uint8_t *payload = buffer + sizeof(message_header_t);

  if (payload_len != 32) {
    printf("Invalid payload length (%d), expected 32 bytes (2 * 16-byte "
           "arrays).\n",
           payload_len);
    return -1;
  }

  uint8_t *first_array = payload;
  uint8_t *second_array = payload + 16;

  size_t first_ip_len = 0;
  for (int i = 0; i < 16; i++) {
    if (first_array[i] == '\0') {
      break;
    }
    first_ip_len++;
  }

  if (first_ip_len == 16) {
    first_ip_len = 15;
  }

  char *src_ip = (char *)malloc(first_ip_len + 1);
  if (src_ip == NULL) {
    fprintf(stderr, "Failed to allocate memory for source IP string.\n");
    return -1;
  }

  memcpy(src_ip, first_array, first_ip_len);
  src_ip[first_ip_len] = '\0';

  size_t second_ip_len = 0;
  for (int i = 0; i < 16; i++) {
    if (second_array[i] == '\0') {
      break;
    }
    second_ip_len++;
  }

  if (second_ip_len == 16) {
    second_ip_len = 15;
  }

  char *dst_ip = (char *)malloc(second_ip_len + 1);
  if (dst_ip == NULL) {
    fprintf(stderr, "Failed to allocate memory for destination IP string.\n");
    free(src_ip);
    return -1;
  }

  memcpy(dst_ip, second_array, second_ip_len);
  dst_ip[second_ip_len] = '\0';

  printf("Extracted source IP: '%s', destination IP: '%s'\n", src_ip, dst_ip);

  if (!is_valid_ipv4(src_ip)) {
    printf("Invalid source IPv4 address format: '%s'.\n", src_ip);
    free(src_ip);
    free(dst_ip);
    return -1;
  }

  if (!is_valid_ipv4(dst_ip)) {
    printf("Invalid destination IPv4 address format: '%s'.\n", dst_ip);
    free(src_ip);
    free(dst_ip);
    return -1;
  }

  printf("Both IPs are valid. Adding IP mapping: %s -> %s\n", src_ip, dst_ip);

  int mapping_result = add_ip_mapping(src_ip, dst_ip);
  if (mapping_result != 0) {
    fprintf(stderr, "Failed to add IP mapping: %s -> %s (error code: %d)\n",
            src_ip, dst_ip, mapping_result);
    free(src_ip);
    free(dst_ip);
    return -1;
  }

  printf("Successfully added IP mapping: %s -> %s\n", src_ip, dst_ip);

  free(src_ip);
  free(dst_ip);
  return 0;
}

/**
 * Processes packet using normal encapsulation and queuing logic.
 *
 * @param buffer Original packet buffer
 * @param len Original packet length
 */
static void process_packet_normally(uint8_t *buffer, size_t len) {
  const task_config_t *config = get_global_task_config();

  if (len <= config->pkt_trim_bytes + 32) {
    printf("Packet too small (%zu bytes) for expected structure (trimsize=%u + "
           "2 * 16-byte IPs).\n",
           len, config->pkt_trim_bytes);
    return;
  }

  uint8_t *first_ip_bytes = buffer + config->pkt_trim_bytes;
  uint8_t *second_ip_bytes = buffer + config->pkt_trim_bytes + 16;

  char first_ip_str[17] = {0};
  char second_ip_str[17] = {0};
  memcpy(first_ip_str, first_ip_bytes, 16);
  memcpy(second_ip_str, second_ip_bytes, 16);
  printf("First IP as string: '%s'\n", first_ip_str);
  printf("Second IP as string: '%s'\n", second_ip_str);

  size_t encap_len;
  uint8_t *encap_data = encapsulate_packet(buffer, len, &encap_len);
  if (encap_data == NULL) {
    fprintf(stderr, "Failed to encapsulate packet. Dropping packet.\n");
    return;
  }

  size_t final_len = 16 + encap_len;
  uint8_t *final_buffer = (uint8_t *)malloc(final_len);
  if (final_buffer == NULL) {
    fprintf(stderr, "Failed to allocate memory for final packet buffer.\n");
    free(encap_data);
    return;
  }

  memcpy(final_buffer, second_ip_bytes, 16);

  memcpy(final_buffer + 16, encap_data, encap_len);

  printf("Final packet size: %zu bytes (second IP + %zu encapsulated data)\n",
         final_len, encap_len);

  int enqueue_result =
      queue_enqueue(packet_receive_queue, final_buffer, final_len);

  if (enqueue_result != 0) {
    fprintf(stderr,
            "Failed to enqueue packet (final size: %zu). Dropping packet.\n",
            final_len);
  }

  free(encap_data);
  free(final_buffer);
}

/**
 * Callback function for handling received raw packets from SITP library.
 * This function enqueues the received data for processing by a separate thread.
 *
 * @param arg User-defined argument passed from sitp_lib_start
 * @param buffer Pointer to the received data buffer
 * @param len Length of the received data
 */
void raw_packet_receive_callback(void *arg, uint8_t *buffer, size_t len) {
  (void)arg;
  printf("--- Queue TUN Packet Details ---\n");
  print_payload(buffer, len);
  printf("----------------------------\n");

  printf("Received packet of %zu bytes, enqueueing for processing\n", len);

  if (packet_receive_queue == NULL) {
    fprintf(stderr, "raw_packet_receive_callback: Packet receive queue is not "
                    "initialized. Dropping packet.\n");
    return;
  }

  uint8_t *trimmed_buffer;
  size_t trimmed_len;
  if (validate_and_trim_packet(buffer, len, &trimmed_buffer, &trimmed_len) !=
      0) {
    process_packet_normally(buffer, len);
    return;
  }

  message_header_t *header;
  int payload_len, msg_type;
  if (validate_message_header(trimmed_buffer, trimmed_len, &header,
                              &payload_len, &msg_type) != 0) {
    process_packet_normally(buffer, len);
    return;
  }

  if (process_config_message(trimmed_buffer, payload_len) == 0) {
    return;
  }

  process_packet_normally(buffer, len);
}

/**
 * Processes and forwards a packet using IP mapping.
 *
 * @param encap_data_from_queue Packet data including 16-byte IP prefix
 * @param encap_len_from_queue Total length of packet data
 * @return 0 if packet was processed successfully, -1 if failed or dropped
 */
static int process_and_forward_packet(uint8_t *encap_data_from_queue,
                                      size_t encap_len_from_queue) {
  if (encap_data_from_queue == NULL || encap_len_from_queue <= 16) {
    if (encap_data_from_queue != NULL) {
      fprintf(stderr,
              "Received packet too small (size: %zu, expected > 16 bytes)\n",
              encap_len_from_queue);
    }
    return -1;
  }

  char target_ip[17] = {0};
  memcpy(target_ip, encap_data_from_queue, 16);

  uint8_t *packet_data = encap_data_from_queue + 16;
  size_t packet_len = encap_len_from_queue - 16;

  printf("Extracted source IP: '%s', packet length: %zu\n", target_ip,
         packet_len);

  const char *dst_ip = get_dst_ip(target_ip);
  if (dst_ip == NULL) {
    fprintf(stderr, "No IP mapping found for source IP '%s', dropping packet\n",
            target_ip);
    return -1;
  }

  printf("Mapped source IP '%s' to destination IP '%s'\n", target_ip, dst_ip);

  int result = global_tcp_client_send(dst_ip, packet_data, packet_len);
  if (result != 0) {
    fprintf(stderr,
            "Failed to send packet to destination server %s (mapped from %s, "
            "error code: %d)\n",
            dst_ip, target_ip, result);
    return -1;
  } else {
    printf("Packet forwarded successfully to %s (mapped from %s)\n", dst_ip,
           target_ip);
    return 0;
  }
}

/**
 * Thread function that processes packets from the queue and sends them to the
 * target server.
 */
static void *packet_receiver_thread_func(void *arg) {
  (void)arg;

  printf("Thread started and waiting for packets\n");
  while (keep_receiver_thread_running) {
    uint8_t *encap_data_from_queue = NULL;
    size_t encap_len_from_queue = 0;

    if (queue_dequeue(packet_receive_queue, &encap_data_from_queue,
                      &encap_len_from_queue) == 0) {
      if (encap_data_from_queue != NULL && encap_len_from_queue > 0) {
        // Check if this is a stop signal (single byte with value 0xFF)
        if (encap_len_from_queue == 1 && encap_data_from_queue[0] == 0xFF) {
          printf("Graceful shutdown signal received, terminating thread\n");
          free(encap_data_from_queue);
          break;
        }

        printf("Processing packet (%zu bytes)\n", encap_len_from_queue);
        int result = process_and_forward_packet(encap_data_from_queue,
                                                encap_len_from_queue);
        if (result != 0) {
          fprintf(stderr, "Warning: Failed to process packet (%zu bytes), error code: %d\n",
                  encap_len_from_queue, result);
        }

        free(encap_data_from_queue);
      } else if (!keep_receiver_thread_running) {
        // Fallback: check the running flag if we get unexpected NULL
        printf("Shutdown flag detected, terminating thread\n");
        if (encap_data_from_queue != NULL) {
          free(encap_data_from_queue);
        }
        break;
      }
    } else {
      if (!keep_receiver_thread_running) {
        printf("Shutdown requested during queue operation, exiting\n");
        break;
      }
      fprintf(stderr, "Warning: Queue dequeue operation failed, retrying...\n");
    }
  }

  printf("Thread terminated gracefully\n");
  return NULL;
}

/**
 * Initialize the packet receiver with a queue of specified capacity.
 */
int init_packet_receiver(size_t queue_capacity) {
  printf("Initializing packet receiver with queue capacity %zu...\n",
         queue_capacity);
  if (packet_receive_queue != NULL) {
    fprintf(stderr,
            "init_packet_receiver: Packet receiver already initialized.\n");
    return -1;
  }

  packet_receive_queue = queue_create(queue_capacity);
  if (packet_receive_queue == NULL) {
    fprintf(stderr,
            "init_packet_receiver: Failed to create packet receive queue.\n");
    return -1;
  }

  keep_receiver_thread_running = 1;
  if (pthread_create(&receiver_thread_id, NULL, packet_receiver_thread_func,
                     NULL) != 0) {
    fprintf(stderr,
            "init_packet_receiver: Failed to create packet receiver thread.\n");
    queue_destroy(packet_receive_queue);
    packet_receive_queue = NULL;
    return -1;
  }

  printf("Packet receiver initialized successfully.\n");
  return 0;
}

/**
 * Clean up resources used by the packet receiver.
 */
void cleanup_packet_receiver(void) {
  printf("Cleaning up packet receiver...\n");
  if (packet_receive_queue == NULL && !keep_receiver_thread_running) {
    printf("Packet receiver already cleaned up or not initialized.\n");
    return;
  }

  if (keep_receiver_thread_running) {
    keep_receiver_thread_running = 0;

    printf("Signaling receiver thread to stop...\n");

    // Send a "poison pill" (special marker) to wake up the blocked dequeue operation
    if (packet_receive_queue != NULL) {
      // Enqueue a special stop signal packet (single byte with value 0xFF)
      // This will wake up the thread from pthread_cond_wait in queue_dequeue
      uint8_t stop_signal = 0xFF;
      if (queue_enqueue(packet_receive_queue, &stop_signal, 1) != 0) {
        fprintf(stderr, "cleanup_packet_receiver: Failed to enqueue stop signal. "
                        "Thread might be stuck.\n");
      }
    }

    if (pthread_join(receiver_thread_id, NULL) != 0) {
      fprintf(stderr, "cleanup_packet_receiver: Failed to join packet receiver "
                      "thread. It might be stuck.\n");
    } else {
      printf("Packet receiver thread joined successfully.\n");
    }
  }

  if (packet_receive_queue != NULL) {
    queue_destroy(packet_receive_queue);
    packet_receive_queue = NULL;
    printf("Packet receive queue destroyed.\n");
  }

  printf("Packet receiver cleanup complete.\n");
}

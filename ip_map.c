#include "ip_map.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>

// Global IP mapping hash table
static ip_mapping_t *ip_map = NULL;

// Store the IP mapping file path for writing new entries
static char ip_mapping_file_path[256] = {0};

/**
 * Verify if IP address format is correct
 *
 * @param ip IP address string
 * @return 1 indicates valid, 0 indicates invalid
 */
static int is_valid_ip(const char *ip) {
    struct sockaddr_in sa;
    return inet_pton(AF_INET, ip, &(sa.sin_addr)) != 0;
}

/**
 * Remove leading and trailing whitespace from string
 *
 * @param str String to be processed
 */
static void trim_whitespace(char *str) {
    char *start = str;
    char *end;

    // Remove leading whitespace
    while (*start == ' ' || *start == '\t' || *start == '\n' || *start == '\r') {
        start++;
    }

    // If string is all whitespace
    if (*start == 0) {
        *str = '\0';
        return;
    }

    // Remove trailing whitespace
    end = start + strlen(start) - 1;
    while (end > start && (*end == ' ' || *end == '\t' || *end == '\n' || *end == '\r')) {
        end--;
    }

    // Add string terminator
    *(end + 1) = '\0';

    // If string beginning was modified, need to move to string start
    if (start != str) {
        memmove(str, start, strlen(start) + 1);
    }
}

/**
 * Parse IP mapping line
 *
 * @param line Line to be parsed
 * @param src_ip Output source IP
 * @param dst_ip Output destination IP
 * @return 0 indicates success, non-zero indicates failure
 */
static int parse_ip_mapping_line(char *line, char *src_ip, char *dst_ip) {
    char *colon_pos;

    // Remove comments
    char *comment_pos = strchr(line, '#');
    if (comment_pos) {
        *comment_pos = '\0';
    }

    // Remove leading and trailing whitespace
    trim_whitespace(line);

    // Skip empty lines
    if (strlen(line) == 0) {
        return -1;
    }

    // Find colon separator
    colon_pos = strchr(line, ':');
    if (!colon_pos) {
        fprintf(stderr, "IP mapping line format error (missing ':'): %s\n", line);
        return -1;
    }

    // Separate source IP and destination IP
    *colon_pos = '\0';
    strcpy(src_ip, line);
    strcpy(dst_ip, colon_pos + 1);

    // Remove whitespace from IP addresses
    trim_whitespace(src_ip);
    trim_whitespace(dst_ip);

    // Validate IP address format
    if (!is_valid_ip(src_ip)) {
        fprintf(stderr, "Invalid source IP address: %s\n", src_ip);
        return -1;
    }

    if (!is_valid_ip(dst_ip)) {
        fprintf(stderr, "Invalid destination IP address: %s\n", dst_ip);
        return -1;
    }

    return 0;
}

/**
 * Add IP mapping to hash table (internal function)
 *
 * @param src_ip Source IP address
 * @param dst_ip Destination IP address
 * @return 0 indicates success, non-zero indicates failure
 */
static int add_ip_mapping_to_table(const char *src_ip, const char *dst_ip) {
    ip_mapping_t *mapping;

    // Check if same source IP already exists
    HASH_FIND_STR(ip_map, src_ip, mapping);
    if (mapping) {
        printf("Warning: Source IP %s already has mapping, will override to %s\n", src_ip, dst_ip);
        strcpy(mapping->dst_ip, dst_ip);
        return 0;
    }

    // Create new mapping entry
    mapping = (ip_mapping_t *)malloc(sizeof(ip_mapping_t));
    if (!mapping) {
        fprintf(stderr, "Memory allocation failed\n");
        return -1;
    }

    strcpy(mapping->src_ip, src_ip);
    strcpy(mapping->dst_ip, dst_ip);

    // Add to hash table
    HASH_ADD_STR(ip_map, src_ip, mapping);

    return 0;
}

int init_ip_map(const char *filename) {
    FILE *file;
    char line[256];
    char src_ip[16], dst_ip[16];
    int line_num = 0;
    int success_count = 0;
    int error_count = 0;

    // Clean up existing mappings (if any)
    cleanup_ip_map();

    // Save the file path for future writes
    strncpy(ip_mapping_file_path, filename, sizeof(ip_mapping_file_path) - 1);
    ip_mapping_file_path[sizeof(ip_mapping_file_path) - 1] = '\0';

    file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Unable to open IP mapping file: %s\n", filename);
        return -1;
    }

    printf("Loading IP mapping file: %s\n", filename);

    // Read file line by line
    while (fgets(line, sizeof(line), file)) {
        line_num++;

        // Parse IP mapping line
        if (parse_ip_mapping_line(line, src_ip, dst_ip) == 0) {
            // Add to hash table
            if (add_ip_mapping_to_table(src_ip, dst_ip) == 0) {
                success_count++;
                printf("Loaded IP mapping: %s -> %s\n", src_ip, dst_ip);
            } else {
                error_count++;
                fprintf(stderr, "Line %d: Failed to add IP mapping\n", line_num);
            }
        } else {
            // If not empty line and not comment line, record error
            trim_whitespace(line);
            if (strlen(line) > 0 && line[0] != '#') {
                error_count++;
                fprintf(stderr, "Line %d: IP mapping format error\n", line_num);
            }
        }
    }

    fclose(file);

    printf("IP mapping loading completed: %d successful, %d errors\n", success_count, error_count);

    return (error_count > 0) ? -1 : 0;
}

const char *get_dst_ip(const char *src_ip) {
    ip_mapping_t *mapping;

    if (!src_ip) {
        return NULL;
    }

    HASH_FIND_STR(ip_map, src_ip, mapping);

    return mapping ? mapping->dst_ip : NULL;
}

void cleanup_ip_map(void) {
    ip_mapping_t *mapping, *tmp;

    HASH_ITER(hh, ip_map, mapping, tmp) {
        HASH_DEL(ip_map, mapping);
        free(mapping);
    }

    ip_map = NULL;
}

unsigned int get_ip_map_count(void) {
    return HASH_COUNT(ip_map);
}

void print_ip_mappings(void) {
    ip_mapping_t *mapping;
    unsigned int count = 0;

    printf("=== IP Mapping Table ===\n");

    for (mapping = ip_map; mapping != NULL; mapping = mapping->hh.next) {
        printf("%s -> %s\n", mapping->src_ip, mapping->dst_ip);
        count++;
    }

    printf("Total: %u mappings\n", count);
    printf("========================\n");
}

int get_all_src_ips(char ***keys, unsigned int *count) {
    ip_mapping_t *mapping;
    char **result = NULL;
    unsigned int total_count = 0;
    unsigned int i = 0;

    if (!keys || !count) {
        return -1;
    }

    *keys = NULL;
    *count = 0;

    // Get total count
    total_count = HASH_COUNT(ip_map);
    if (total_count == 0) {
        return 0;
    }

    // Allocate memory for the array
    result = (char **)malloc(total_count * sizeof(char *));
    if (!result) {
        fprintf(stderr, "Memory allocation failed for source IP array\n");
        return -1;
    }

    // Copy all source IPs (keys are already unique in hash table)
    for (mapping = ip_map; mapping != NULL; mapping = mapping->hh.next) {
        result[i] = (char *)malloc(16 * sizeof(char));
        if (!result[i]) {
            fprintf(stderr, "Memory allocation failed for source IP string\n");
            // Free previously allocated memory
            for (unsigned int j = 0; j < i; j++) {
                free(result[j]);
            }
            free(result);
            return -1;
        }
        strcpy(result[i], mapping->src_ip);
        i++;
    }

    *keys = result;
    *count = total_count;
    return 0;
}

int get_all_dst_ips(char ***values, unsigned int *count) {
    ip_mapping_t *mapping;
    char **temp_array = NULL;
    char **result = NULL;
    unsigned int total_count = 0;
    unsigned int unique_count = 0;
    unsigned int i = 0, j = 0;
    int found = 0;

    if (!values || !count) {
        return -1;
    }

    *values = NULL;
    *count = 0;

    // Get total count
    total_count = HASH_COUNT(ip_map);
    if (total_count == 0) {
        return 0;
    }

    // First pass: collect all destination IPs in a temporary array
    temp_array = (char **)malloc(total_count * sizeof(char *));
    if (!temp_array) {
        fprintf(stderr, "Memory allocation failed for temporary array\n");
        return -1;
    }

    for (mapping = ip_map; mapping != NULL; mapping = mapping->hh.next) {
        temp_array[i] = (char *)malloc(16 * sizeof(char));
        if (!temp_array[i]) {
            fprintf(stderr, "Memory allocation failed for destination IP string\n");
            // Free previously allocated memory
            for (unsigned int k = 0; k < i; k++) {
                free(temp_array[k]);
            }
            free(temp_array);
            return -1;
        }
        strcpy(temp_array[i], mapping->dst_ip);
        i++;
    }

    // Second pass: remove duplicates
    result = (char **)malloc(total_count * sizeof(char *));
    if (!result) {
        fprintf(stderr, "Memory allocation failed for destination IP array\n");
        // Free temporary array
        for (unsigned int k = 0; k < total_count; k++) {
            free(temp_array[k]);
        }
        free(temp_array);
        return -1;
    }

    for (i = 0; i < total_count; i++) {
        found = 0;
        // Check if current IP already exists in result array
        for (j = 0; j < unique_count; j++) {
            if (strcmp(temp_array[i], result[j]) == 0) {
                found = 1;
                break;
            }
        }

        // If not found, add to result array
        if (!found) {
            result[unique_count] = (char *)malloc(16 * sizeof(char));
            if (!result[unique_count]) {
                fprintf(stderr, "Memory allocation failed for unique destination IP string\n");
                // Free previously allocated memory
                for (unsigned int k = 0; k < unique_count; k++) {
                    free(result[k]);
                }
                free(result);
                for (unsigned int k = 0; k < total_count; k++) {
                    free(temp_array[k]);
                }
                free(temp_array);
                return -1;
            }
            strcpy(result[unique_count], temp_array[i]);
            unique_count++;
        }
    }

    // Free temporary array
    for (i = 0; i < total_count; i++) {
        free(temp_array[i]);
    }
    free(temp_array);

    // Resize result array to fit exactly the unique count
    if (unique_count < total_count) {
        char **resized_result = (char **)realloc(result, unique_count * sizeof(char *));
        if (resized_result) {
            result = resized_result;
        }
        // If realloc fails, continue with original result (not a critical error)
    }

    *values = result;
    *count = unique_count;
    return 0;
}

void free_ip_array(char **array, unsigned int count) {
    if (!array) {
        return;
    }

    for (unsigned int i = 0; i < count; i++) {
        if (array[i]) {
            free(array[i]);
        }
    }
    free(array);
}

/**
 * Add a new IP mapping pair to both the memory table and the file
 *
 * @param src_ip Source IP address string
 * @param dst_ip Destination IP address string
 * @return 0 indicates success, non-zero indicates failure
 */
int add_ip_mapping(const char *src_ip, const char *dst_ip) {
    FILE *file;
    ip_mapping_t *existing_mapping;
    int is_update = 0;

    // Validate input parameters
    if (!src_ip || !dst_ip) {
        fprintf(stderr, "Error: Source IP or destination IP is NULL\n");
        return -1;
    }

    // Validate IP address format
    if (!is_valid_ip(src_ip)) {
        fprintf(stderr, "Error: Invalid source IP address: %s\n", src_ip);
        return -1;
    }

    if (!is_valid_ip(dst_ip)) {
        fprintf(stderr, "Error: Invalid destination IP address: %s\n", dst_ip);
        return -1;
    }

    // Check if IP mapping file path is available
    if (strlen(ip_mapping_file_path) == 0) {
        fprintf(stderr, "Error: IP mapping file path not initialized. Call init_ip_map() first.\n");
        return -1;
    }

    // Check if src_ip already exists
    HASH_FIND_STR(ip_map, src_ip, existing_mapping);
    if (existing_mapping) {
        is_update = 1;
    }

    // Add to memory table first
    if (add_ip_mapping_to_table(src_ip, dst_ip) != 0) {
        fprintf(stderr, "Error: Failed to add IP mapping to memory table\n");
        return -1;
    }

    if (is_update) {
        // If updating existing mapping, need to rewrite the entire file
        file = fopen(ip_mapping_file_path, "w");
        if (!file) {
            fprintf(stderr, "Error: Unable to open IP mapping file for writing: %s\n", ip_mapping_file_path);
            return -1;
        }

        // Write all current mappings from memory to file
        ip_mapping_t *mapping;
        for (mapping = ip_map; mapping != NULL; mapping = mapping->hh.next) {
            fprintf(file, "%s:%s\n", mapping->src_ip, mapping->dst_ip);
        }
        fclose(file);

        printf("Successfully updated IP mapping: %s -> %s\n", src_ip, dst_ip);
    } else {
        // If adding new mapping, just append to file
        file = fopen(ip_mapping_file_path, "a");
        if (!file) {
            fprintf(stderr, "Error: Unable to open IP mapping file for appending: %s\n", ip_mapping_file_path);
            // Remove from memory table since file write failed
            ip_mapping_t *mapping;
            HASH_FIND_STR(ip_map, src_ip, mapping);
            if (mapping) {
                HASH_DEL(ip_map, mapping);
                free(mapping);
            }
            return -1;
        }

        // Write the new mapping to file
        fprintf(file, "%s:%s\n", src_ip, dst_ip);
        fclose(file);

        printf("Successfully added IP mapping: %s -> %s\n", src_ip, dst_ip);
    }

    return 0;
}

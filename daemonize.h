#ifndef __DAEMONIZE_H__
#define __DAEMONIZE_H__

#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>


#ifndef PID_FILE
#define PID_FILE "/var/run/tun_run.pid"
#endif

int daemon_start();

int daemon_main(int argc, char const *argv[]);

// External cleanup function that can be called from signal handlers
void daemon_cleanup(void);

#endif // __DAEMONIZE_H__

CC=gcc
CFLAGS=-Wall -Wextra -O2 -Wl,-rpath,'$$ORIGIN:$$ORIGIN/lib' -Ilibevent/include
LDLIBS=-Llib -Llibevent/lib -lsitp_lib -lpthread -levent_core -levent_pthreads
TARGET=tun_server

# Control whether to use color for logging. Default is 1 (on).
# Can be disabled from command line: make LOG_USE_COLOR=0
LOG_USE_COLOR ?= 1
ifeq ($(LOG_USE_COLOR), 1)
CFLAGS += -DLOG_USE_COLOR
endif

SOURCES = $(wildcard *.c)
OBJS = $(SOURCES:.c=.o)

all: $(TARGET)

$(TARGET): $(OBJS)
	$(CC) $(CFLAGS) -o $(TARGET) $(OBJS) $(LDLIBS)

%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	rm -f $(TARGET) *.o

.PHONY: all clean

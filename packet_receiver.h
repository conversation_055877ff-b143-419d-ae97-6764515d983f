#ifndef PACKET_RECEIVER_H
#define PACKET_RECEIVER_H

#include <event2/bufferevent.h>
#include <stddef.h>
#include <stdint.h>
#include "message_common.h" // For message_header_t

// States for the packet reception state machine
typedef enum {
  STATE_WAITING_HEADER, // Waiting for the magic string and length field
  STATE_WAITING_DATA    // Waiting for the complete data payload
} packet_receiver_state_t;

// Structure to hold the state of packet reception for each connection
typedef struct {
  packet_receiver_state_t state; // Current state
  uint8_t header_buffer[sizeof(message_header_t)]; // Buffer for the header (message_header_t)
  size_t header_bytes_received; // Number of header bytes received so far
  uint8_t *data_buffer;         // Buffer for the data payload
  int data_length;              // Expected length of the data payload (matching message_header_t.len)
  size_t data_bytes_received;   // Number of data bytes received so far
} packet_receiver_context_t;

// Initialize a new packet receiver context
packet_receiver_context_t *packet_receiver_init(void);

// Clean up a packet receiver context
void packet_receiver_cleanup(packet_receiver_context_t *ctx);

// Process incoming data according to the SITP protocol
// Returns 0 on success, non-zero on error
int packet_receiver_process_data(struct bufferevent *bev,
                                 packet_receiver_context_t *ctx);

#endif // PACKET_RECEIVER_H

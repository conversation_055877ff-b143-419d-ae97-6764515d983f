#ifndef MESSAGE_COMMON_H
#define MESSAGE_COMMON_H

#include <stdint.h>

#define MAGIC_NUMBER "JQTB"
#define MAGIC_LEN 4

// Message Types
#define MSG_TYPE_HEARTBEAT 1
#define MSG_TYPE_AUTHENTICATION 2
#define MSG_TYPE_CONFIG 3
#define MSG_TYPE_DATA_SYNC 4

// Maximum payload size to prevent memory exhaustion attacks
#define MAX_PAYLOAD_SIZE (1024 * 1024) // 1MB maximum payload
#define MIN_PAYLOAD_SIZE 0             // Minimum payload size (can be 0)

// Message type specific payload limits
#define MAX_HEARTBEAT_PAYLOAD_SIZE 1024
#define MAX_AUTH_PAYLOAD_SIZE      4096
#define MAX_CONFIG_PAYLOAD_SIZE    8192
#define MAX_UNKNOWN_PAYLOAD_SIZE   512
#define LARGE_MESSAGE_THRESHOLD    (64 * 1024)

// Structure to hold message header
typedef struct {
  char magic[MAGIC_LEN];
  int len;    // little endian
  int type;   // little endian
} message_header_t;

#endif /* MESSAGE_COMMON_H */

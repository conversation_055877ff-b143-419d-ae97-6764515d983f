#include "hex_utils.h"

// Print data in 2-byte hex groups with ASCII, similar to `tcpdump -X`
void print_hex_ascii_line(const unsigned char *payload, int len, int offset) {
  int i;
  int hex_chars_count = 0;
  // Max hex characters for a 16-byte line.
  const int max_hex_display_width = (16 / 2) * 4 + (16 / 2 - 1);

  printf("%04x: ", offset);

  // Print hex in 2-byte groups.
  for (i = 0; i < len; i += 2) {
    if (i > 0) {
      printf(" ");
      hex_chars_count++;
    }
    if (i + 1 < len) {
      printf("%02x%02x", payload[i], payload[i + 1]);
      hex_chars_count += 4;
    } else {
      // Handle odd byte at the end of the payload.
      printf("%02x  ", payload[i]);
      hex_chars_count += 4;
    }
  }

  // Pad with spaces to align ASCII output.
  for (i = hex_chars_count; i < max_hex_display_width; i++) {
    printf(" ");
  }

  printf("  "); // Separator

  // Print ASCII representation.
  for (i = 0; i < len; i++) {
    if (isprint(payload[i]))
      printf("%c", payload[i]);
    else
      printf(".");
  }
  printf("\n");
}

// Print packet data.
void print_payload(const unsigned char *payload, int len) {
  int len_rem = len;
  int line_width = 16; // Bytes per line
  int line_len;
  int offset = 0;
  const unsigned char *ch = payload;

  if (len <= 0)
    return;

  printf("Payload (len %d):\n", len);

  if (len <= line_width) {
    print_hex_ascii_line(ch, len, offset);
    return;
  }

  // Print line by line for payloads larger than line_width.
  for (;;) {
    line_len = line_width;
    if (line_len > len_rem)
      line_len = len_rem;

    print_hex_ascii_line(ch, line_len, offset);

    len_rem -= line_len;
    ch += line_len;
    offset += line_len;

    if (len_rem <= 0) {
      break;
    }
  }
}

#ifndef TCP_CLIENT_H
#define TCP_CLIENT_H

#include <stddef.h>

// Forward declaration of the client structure
typedef struct tcp_client tcp_client_t;

/**
 * Initialize a new TCP client instance with the specified target IP and port.
 *
 * @param target_ip The target IP address
 * @param target_port The target port
 * @return Pointer to the client instance on success, NULL on failure
 */
tcp_client_t *tcp_client_init(const char *target_ip,
                              unsigned short target_port);

/**
 * Clean up resources used by the TCP client instance.
 *
 * @param client Pointer to the client instance
 */
void tcp_client_cleanup(tcp_client_t *client);

/**
 * Send data to the target server.
 *
 * @param client Pointer to the client instance
 * @param data Pointer to the data buffer
 * @param len Length of the data
 * @return 0 on success, non-zero on failure
 */
int tcp_client_send(tcp_client_t *client, const void *data, size_t len);

/**
 * Initialize a global TCP client with the specified target IP and port.
 * If a client for this IP already exists, it will be replaced.
 *
 * @param target_ip The target IP address
 * @param target_port The target port
 * @return 0 on success, non-zero on failure
 */
int init_global_tcp_client(const char *target_ip, unsigned short target_port);

/**
 * Get the global TCP client instance for the specified IP.
 *
 * @param target_ip The target IP address
 * @return Pointer to the client instance, NULL if not found
 */
tcp_client_t *get_global_tcp_client(const char *target_ip);

/**
 * Send data using the global TCP client for the specified IP.
 *
 * @param target_ip The target IP address
 * @param data Pointer to the data buffer
 * @param len Length of the data
 * @return 0 on success, non-zero on failure
 */
int global_tcp_client_send(const char *target_ip, const void *data, size_t len);

/**
 * Clean up the global TCP client for the specified IP.
 *
 * @param target_ip The target IP address, NULL to clean up all clients
 */
void cleanup_global_tcp_client(const char *target_ip);

/**
 * Get the count of global TCP clients.
 *
 * @return Number of global TCP clients
 */
unsigned int get_global_tcp_client_count(void);

/**
 * List all global TCP client IPs.
 *
 * @param ips Array to store IP addresses (caller must provide sufficient space)
 * @param max_count Maximum number of IPs to return
 * @return Number of IPs returned
 */
unsigned int list_global_tcp_client_ips(char ips[][40], unsigned int max_count);

#endif // TCP_CLIENT_H

#include "daemonize.h"
#include <errno.h>

// Global variable for cleanup in signal handlers
static volatile sig_atomic_t daemon_should_exit = 0;

void write_pid_file() {
  FILE *f = fopen(PID_FILE, "w");
  if (f == NULL) {
    perror("fopen");
    exit(EXIT_FAILURE);
  }
  if (fprintf(f, "%d\n", getpid()) < 0) {
    perror("fprintf");
    fclose(f);
    exit(EXIT_FAILURE);
  }
  if (fclose(f) != 0) {
    perror("fclose");
    exit(EXIT_FAILURE);
  }
}

void remove_pid_file() {
  if (unlink(PID_FILE) != 0) {
    if (errno != ENOENT) {
      perror("unlink");
    }
  }
}

int read_pid_file() {
  FILE *f = fopen(PID_FILE, "r");
  if (f == NULL) {
    return -1;
  }
  int pid;
  int result = fscanf(f, "%d", &pid);
  if (fclose(f) != 0) {
    perror("fclose");
  }

  if (result != 1) {
    fprintf(stderr, "Error reading PID from file\n");
    return -1;
  }

  // Validate PID value
  if (pid <= 0) {
    fprintf(stderr, "Invalid PID in file: %d\n", pid);
    return -1;
  }

  return pid;
}

void daemonize() {
  pid_t pid = fork();
  if (pid < 0) {
    perror("fork");
    exit(EXIT_FAILURE);
  }
  if (pid > 0) {
    exit(EXIT_SUCCESS);
  }
  if (setsid() < 0) {
    perror("setsid");
    exit(EXIT_FAILURE);
  }

  // Setup signal actions for daemon initialization
  struct sigaction sa_ign;
  sa_ign.sa_handler = SIG_IGN;
  sigemptyset(&sa_ign.sa_mask);
  sa_ign.sa_flags = 0;

  sigaction(SIGCHLD, &sa_ign, NULL);
  sigaction(SIGHUP, &sa_ign, NULL);

  pid = fork();
  if (pid < 0) {
    perror("fork");
    exit(EXIT_FAILURE);
  }
  if (pid > 0) {
    exit(EXIT_SUCCESS);
  }

  umask(0);
  // chdir("/");

  // Close all open file descriptors
  long max_fd = sysconf(_SC_OPEN_MAX);
  if (max_fd == -1) {
    max_fd = 1024; // Default value
  }

  int x;
  for (x = max_fd - 1; x >= 0; x--) {
    close(x);
  }

  // Redirect standard input/output/error to /dev/null
  int fd0 = open("/dev/null", O_RDONLY);
  int fd1 = open("/dev/null", O_WRONLY);
  int fd2 = open("/dev/null", O_WRONLY);

  if (fd0 != STDIN_FILENO || fd1 != STDOUT_FILENO || fd2 != STDERR_FILENO) {
    fprintf(stderr, "Error redirecting standard file descriptors\n");
    exit(EXIT_FAILURE);
  }
}

void signal_handler(int signum) {
  // Use signal-safe functions
  const char msg[] = "Signal received, initiating shutdown...\n";
  write(STDERR_FILENO, msg, sizeof(msg) - 1);

  daemon_should_exit = 1;

  // For SIGTERM, perform cleanup and exit
  if (signum == SIGTERM || signum == SIGINT) {
    // Call comprehensive cleanup function
    daemon_cleanup();
    remove_pid_file();
    exit(EXIT_SUCCESS);
  }
}

void start() {
  int pid = read_pid_file();
  if (pid != -1) {
    if (kill(pid, 0) == 0) {
      printf("Daemon already running with PID %d\n", pid);
      exit(EXIT_FAILURE);
    } else {
      printf("Stale PID file found. Removing and starting new daemon.\n");
      remove_pid_file();
    }
  }

  daemonize();
  write_pid_file();

  // Setup signal handler using sigaction for better reliability
  struct sigaction sa;
  sa.sa_handler = signal_handler;
  sigemptyset(&sa.sa_mask);
  sa.sa_flags = 0;

  // Register handlers for important signals
  if (sigaction(SIGINT, &sa, NULL) == -1) {
    perror("Failed to set SIGINT handler");
  }
  if (sigaction(SIGTERM, &sa, NULL) == -1) {
    perror("Failed to set SIGTERM handler");
  }
  if (sigaction(SIGQUIT, &sa, NULL) == -1) {
    perror("Failed to set SIGQUIT handler");
  }
  if (sigaction(SIGHUP, &sa, NULL) == -1) {
    perror("Failed to set SIGHUP handler");
  }

  // Keep some important signal handlers for debugging/monitoring
  if (sigaction(SIGUSR1, &sa, NULL) == -1) {
    perror("Failed to set SIGUSR1 handler");
  }
  if (sigaction(SIGUSR2, &sa, NULL) == -1) {
    perror("Failed to set SIGUSR2 handler");
  }

  daemon_start();
}

void stop() {
  int pid = read_pid_file();
  if (pid == -1) {
    printf("No daemon running\n");
    exit(EXIT_FAILURE);
  }

  // First try graceful shutdown
  if (kill(pid, SIGTERM) != 0) {
    perror("kill");
    exit(EXIT_FAILURE);
  }

  // Wait for process to terminate
  int attempts = 0;
  const int max_attempts = 10;
  while (attempts < max_attempts) {
    if (kill(pid, 0) != 0) {
      // Process has terminated
      break;
    }
    sleep(1);
    attempts++;
  }

  // If process is still running, force termination
  if (attempts >= max_attempts) {
    printf("Process did not terminate gracefully, forcing termination...\n");
    if (kill(pid, SIGKILL) != 0) {
      perror("kill SIGKILL");
    }
    sleep(1);
  }

  remove_pid_file();
  printf("Daemon stopped\n");
}

void restart() {
  int pid = read_pid_file();
  if (pid != -1) {
    printf("Stopping daemon...\n");
    stop();
    sleep(1); // Brief wait to ensure cleanup completion
  }
  printf("Starting daemon...\n");
  start();
}

int daemon_main(int argc, char const *argv[]) {
  if (argc != 2) {
    fprintf(stderr, "Usage: %s {start|stop|restart|run}\n", argv[0]);
    exit(EXIT_FAILURE);
  }

  if (strcmp(argv[1], "start") == 0) {
    start();
  } else if (strcmp(argv[1], "stop") == 0) {
    stop();
  } else if (strcmp(argv[1], "restart") == 0) {
    restart();
  } else if (strcmp(argv[1], "run") == 0) {
    daemon_start();
  } else {
    fprintf(stderr, "Invalid argument: %s\n", argv[1]);
    exit(EXIT_FAILURE);
  }

  return 0;
}

#ifndef SEND_UTILS_H
#define SEND_UTILS_H

#include <stddef.h>
#include <stdint.h>

/**
 * Initializes the SITP sender with the specified queue capacity.
 *
 * @param queue_capacity The maximum number of packets that can be queued for
 * sending.
 * @return 0 on success, -1 on failure.
 */
int init_sitp_sender(size_t queue_capacity);

/**
 * Cleans up resources used by the SITP sender.
 * This function should be called before the program exits.
 */
void cleanup_sitp_sender(void);

/**
 * Processes and sends a packet through the SITP sender.
 *
 * @param buffer Pointer to the packet data.
 * @param len Length of the packet data.
 * @return 0 on success, -1 on failure.
 */
int process_and_send_packet(uint8_t *buffer, size_t len);

#endif // SEND_UTILS_H

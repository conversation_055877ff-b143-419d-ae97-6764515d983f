#ifndef __SITP_LIB_H__
#define __SITP_LIB_H__

#include <stdint.h>
#include <stddef.h>

typedef void (*sitp_lib_recv_data_func)(void *arg, uint8_t *buffer, size_t len);

void sitp_lib_start(const char *eth_dev, int mtu, uint16_t protocol, uint16_t local_id, uint16_t remote_id,
                    uint16_t local_port, uint16_t remote_port, sitp_lib_recv_data_func pfn_recv, void *arg);

int sitp_lib_send(uint8_t *buffer, size_t len);

#endif // __SITP_LIB_H__

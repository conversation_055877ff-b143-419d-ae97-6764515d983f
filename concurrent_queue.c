#include "concurrent_queue.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

queue_t *queue_create(size_t capacity) {
  if (capacity == 0) {
    return NULL;
  }

  queue_t *q = (queue_t *)malloc(sizeof(queue_t));
  if (!q) {
    return NULL;
  }

  q->elements = (queue_element_t *)malloc(capacity * sizeof(queue_element_t));
  if (!q->elements) {
    free(q);
    return NULL;
  }

  q->capacity = capacity;
  q->count = 0;
  q->head = 0;
  q->tail = 0;

  if (pthread_mutex_init(&q->mutex, NULL) != 0) {
    free(q->elements);
    free(q);
    return NULL;
  }
  if (pthread_cond_init(&q->not_empty, NULL) != 0) {
    pthread_mutex_destroy(&q->mutex);
    free(q->elements);
    free(q);
    return NULL;
  }
  if (pthread_cond_init(&q->not_full, NULL) != 0) {
    pthread_cond_destroy(&q->not_empty);
    pthread_mutex_destroy(&q->mutex);
    free(q->elements);
    free(q);
    return NULL;
  }

  return q;
}

void queue_destroy(queue_t *q) {
  if (!q) {
    return;
  }

  pthread_mutex_lock(&q->mutex);
  // Free any remaining buffers in the queue
  for (size_t i = 0; i < q->count; ++i) {
    size_t current_index = (q->head + i) % q->capacity;
    if (q->elements[current_index].buffer) {
      free(q->elements[current_index].buffer);
      q->elements[current_index].buffer = NULL;
    }
  }
  pthread_mutex_unlock(&q->mutex);

  pthread_cond_destroy(&q->not_full);
  pthread_cond_destroy(&q->not_empty);
  pthread_mutex_destroy(&q->mutex);

  free(q->elements);
  free(q);
}

int queue_enqueue(queue_t *q, const uint8_t *buffer, size_t len) {
  if (!q || !buffer || len == 0) {
    return -1;
  }

  pthread_mutex_lock(&q->mutex);

  while (q->count == q->capacity) {
    // Queue is full, wait for space
    pthread_cond_wait(&q->not_full, &q->mutex);
  }

  // Allocate memory for the new buffer and copy data
  uint8_t *new_buffer = (uint8_t *)malloc(len);
  if (!new_buffer) {
    pthread_mutex_unlock(&q->mutex);
    return -1;
  }
  memcpy(new_buffer, buffer, len);

  q->elements[q->tail].buffer = new_buffer;
  q->elements[q->tail].len = len;
  q->tail = (q->tail + 1) % q->capacity;
  q->count++;

  pthread_cond_signal(&q->not_empty);

  pthread_mutex_unlock(&q->mutex);
  return 0;
}

int queue_dequeue(queue_t *q, uint8_t **buffer, size_t *len) {
  if (!q || !buffer || !len) {
    return -1;
  }

  pthread_mutex_lock(&q->mutex);

  while (q->count == 0) {
    // Queue is empty, wait for an item
    pthread_cond_wait(&q->not_empty, &q->mutex);
  }

  *buffer = q->elements[q->head].buffer;
  *len = q->elements[q->head].len;

  // Mark the slot as "empty" for clarity, though not strictly necessary
  // as the buffer pointer is now owned by the caller.
  q->elements[q->head].buffer = NULL;
  q->elements[q->head].len = 0;

  q->head = (q->head + 1) % q->capacity;
  q->count--;

  pthread_cond_signal(&q->not_full);

  pthread_mutex_unlock(&q->mutex);
  return 0;
}

#include "config_loader.h"
#include "ini.h"
#include "log.h"
#include <ctype.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Helper macros for parsing values
#define PARSE_STRING(dest, src, max_len)                                       \
  do {                                                                         \
    strncpy((dest), (src), (max_len) - 1);                                     \
    (dest)[(max_len) - 1] = '\0';                                              \
  } while (0)

#define PARSE_INT(dest, src)                                                   \
  do {                                                                         \
    (dest) = atoi(src);                                                        \
  } while (0)

#define PARSE_U16(dest, src)                                                   \
  do {                                                                         \
    (dest) = (uint16_t)strtol((src), NULL, 0);                                 \
  } while (0)

#define PARSE_UINT(dest, src)                                                  \
  do {                                                                         \
    (dest) = (unsigned int)strtoul((src), NULL, 0);                            \
  } while (0)

#define PARSE_BOOL(dest, src)                                                  \
  do {                                                                         \
    if (strcasecmp(src, "true") == 0 || strcmp(src, "1") == 0) {               \
      (dest) = 1;                                                              \
    } else {                                                                   \
      (dest) = 0;                                                              \
    }                                                                          \
  } while (0)

// Global configuration instances
task_config_t g_task_config_instance;
server_config_t g_server_config_instance;
int g_task_config_initialized = 0;
int g_server_config_initialized = 0;

// Forward declarations for static functions
static char *trim_whitespace(char *str);
static int parse_log_level(const char *str);
static void set_task_config_defaults(task_config_t *config);
static void print_task_config(const task_config_t *config,
                              const char *filename);
static int parse_sitp_task_kv(task_config_t *pconfig, const char *name,
                              const char *value);
static int task_config_handler(void *user, const char *section,
                               const char *name, const char *value);
static void set_server_config_defaults(server_config_t *config);
static void print_server_config(const server_config_t *config,
                                const char *filename);
static int parse_server_kv(server_config_t *pconfig, const char *name,
                           const char *value);
static int server_config_handler(void *user, const char *section,
                                 const char *name, const char *value);

static char *trim_whitespace(char *str) {
  char *end;

  // Trim leading space
  while (isspace((unsigned char)*str))
    str++;

  if (*str == 0) // All spaces?
    return str;

  // Trim trailing space
  end = str + strlen(str) - 1;
  while (end > str && isspace((unsigned char)*end))
    end--;

  // Write new null terminator
  *(end + 1) = 0;

  return str;
}

static int parse_log_level(const char *str) {
  if (strcasecmp(str, "TRACE") == 0)
    return LOG_TRACE;
  if (strcasecmp(str, "DEBUG") == 0)
    return LOG_DEBUG;
  if (strcasecmp(str, "INFO") == 0)
    return LOG_INFO;
  if (strcasecmp(str, "WARN") == 0)
    return LOG_WARN;
  if (strcasecmp(str, "ERROR") == 0)
    return LOG_ERROR;
  if (strcasecmp(str, "FATAL") == 0)
    return LOG_FATAL;
  return LOG_INFO; // Default to INFO for unknown values
}

static int parse_sitp_task_kv(task_config_t *pconfig, const char *name,
                              const char *value) {
  if (strcmp(name, "eth_dev") == 0) {
    PARSE_STRING(pconfig->eth_dev, value, MAX_ETH_DEV_LEN);
  } else if (strcmp(name, "mtu") == 0) {
    PARSE_INT(pconfig->mtu, value);
  } else if (strcmp(name, "protocol") == 0) {
    PARSE_U16(pconfig->protocol, value);
  } else if (strcmp(name, "local_id") == 0) {
    PARSE_U16(pconfig->local_id, value);
  } else if (strcmp(name, "remote_id") == 0) {
    PARSE_U16(pconfig->remote_id, value);
  } else if (strcmp(name, "local_port") == 0) {
    PARSE_U16(pconfig->local_port, value);
  } else if (strcmp(name, "remote_port") == 0) {
    PARSE_U16(pconfig->remote_port, value);
  } else if (strcmp(name, "sitp_q_cap") == 0) {
    PARSE_INT(pconfig->sitp_q_cap, value);
  } else if (strcmp(name, "send_delay") == 0) {
    PARSE_UINT(pconfig->send_delay, value);
  } else if (strcmp(name, "pkt_trim_bytes") == 0) {
    PARSE_UINT(pconfig->pkt_trim_bytes, value);
  } else {
    return 0; // Unknown name
  }
  return 1; // Success
}

static int task_config_handler(void *user, const char *section,
                               const char *name, const char *value) {
  task_config_t *pconfig = (task_config_t *)user;
  char *value_copy, *trimmed_value;
  int result = 0;

  if (strcmp(section, "sitp_task") != 0) {
    return 1; // Not our section, successfully ignored
  }

  value_copy = strdup(value);
  if (!value_copy) {
    fprintf(stderr, "Memory allocation error in task_config_handler\n");
    return 0; // Allocation error
  }
  trimmed_value = trim_whitespace(value_copy);

  result = parse_sitp_task_kv(pconfig, name, trimmed_value);
  if (!result) {
    fprintf(stderr, "Unknown config name in section '%s': %s\n", section, name);
  }

  free(value_copy);
  return result;
}

static int parse_server_kv(server_config_t *pconfig, const char *name,
                           const char *value) {
  if (strcmp(name, "listen_port") == 0) {
    PARSE_U16(pconfig->listen_port, value);
  } else if (strcmp(name, "listen_ip") == 0) {
    PARSE_STRING(pconfig->listen_ip, value, MAX_IP_LEN);
  } else if (strcmp(name, "log_to_file") == 0) {
    PARSE_BOOL(pconfig->log_to_file, value);
  } else if (strcmp(name, "log_filename") == 0) {
    PARSE_STRING(pconfig->log_filename, value, MAX_LOG_FILENAME_LEN);
  } else if (strcmp(name, "log_level") == 0) {
    pconfig->log_level = parse_log_level(value);
  } else {
    return 0; // Unknown name
  }
  return 1; // Success
}

static int server_config_handler(void *user, const char *section,
                                 const char *name, const char *value) {
  server_config_t *pconfig = (server_config_t *)user;
  char *value_copy, *trimmed_value;
  int result = 0;

  if (strcmp(section, "server") != 0) {
    return 1; // Not our section, successfully ignored
  }

  value_copy = strdup(value);
  if (!value_copy) {
    fprintf(stderr, "Memory allocation error in server_config_handler\n");
    return 0; // Allocation error
  }
  trimmed_value = trim_whitespace(value_copy);

  result = parse_server_kv(pconfig, name, trimmed_value);
  if (!result) {
    fprintf(stderr, "Unknown config name in section '%s': %s\n", section, name);
  }

  free(value_copy);
  return result;
}

static void set_task_config_defaults(task_config_t *config) {
  PARSE_STRING(config->eth_dev, DEFAULT_ETH_DEV, MAX_ETH_DEV_LEN);
  config->mtu = DEFAULT_MTU;
  config->protocol = DEFAULT_PROTOCOL;
  config->local_id = DEFAULT_LOCAL_ID;
  config->remote_id = DEFAULT_REMOTE_ID;
  config->local_port = DEFAULT_LOCAL_PORT;
  config->remote_port = DEFAULT_REMOTE_PORT;
  config->sitp_q_cap = DEFAULT_SITP_Q_CAP;
  config->send_delay = DEFAULT_SEND_DELAY;
  config->pkt_trim_bytes = DEFAULT_PKT_TRIM_BYTES;
}

static void print_task_config(const task_config_t *config,
                              const char *filename) {
  printf("Task configuration loaded from '%s':\n", filename);
  printf("  eth_dev = %s\n", config->eth_dev);
  printf("  mtu = %d\n", config->mtu);
  printf("  protocol = 0x%X\n", config->protocol);
  printf("  local_id = %u\n", config->local_id);
  printf("  remote_id = %u\n", config->remote_id);
  printf("  local_port = %u\n", config->local_port);
  printf("  remote_port = %u\n", config->remote_port);
  printf("  sitp_q_cap = %d\n", config->sitp_q_cap);
  printf("  send_delay = %u\n", config->send_delay);
  printf("  pkt_trim_bytes = %u\n", config->pkt_trim_bytes);
}

static void set_server_config_defaults(server_config_t *config) {
  config->listen_port = DEFAULT_MAIN_LISTEN_PORT;
  PARSE_STRING(config->listen_ip, DEFAULT_MAIN_LISTEN_IP, MAX_IP_LEN);
  config->log_to_file = DEFAULT_LOG_TO_FILE;
  PARSE_STRING(config->log_filename, DEFAULT_LOG_FILENAME,
               MAX_LOG_FILENAME_LEN);
  config->log_level = DEFAULT_LOG_LEVEL;
}

static void print_server_config(const server_config_t *config,
                                const char *filename) {
  printf("Server configuration loaded from '%s':\n", filename);
  printf("  listen_port = %u\n", config->listen_port);
  printf("  listen_ip = %s\n", config->listen_ip);
  printf("  log_to_file = %s\n", config->log_to_file ? "true" : "false");
  printf("  log_filename = %s\n", config->log_filename);
  printf("  log_level = %s\n", log_level_string(config->log_level));
}

int load_task_config(const char *filename, task_config_t *config) {
  set_task_config_defaults(config);

  if (ini_parse(filename, task_config_handler, config) < 0) {
    printf("Can't load '%s' for task_config, using default values.\n",
           filename);
  }

  print_task_config(config, filename);
  return 0;
}

int load_server_config(const char *filename, server_config_t *config) {
  set_server_config_defaults(config);

  if (ini_parse(filename, server_config_handler, config) < 0) {
    printf("Can't load '%s' for server_config, using default values.\n",
           filename);
  }

  print_server_config(config, filename);
  return 0;
}

void initialize_global_task_config(const char *filename) {
  if (g_task_config_initialized) {
    return;
  }

  if (load_task_config(filename, &g_task_config_instance) == 0) {
    printf("Global task configuration initialized successfully from '%s'.\n",
           filename);
  } else {
    printf(
        "Failed to load global task configuration from '%s'. Using defaults.\n",
        filename);
  }
  g_task_config_initialized = 1;
}

const task_config_t *get_global_task_config(void) {
  if (!g_task_config_initialized) {
    fprintf(stderr,
            "Warning: Global task config accessed before initialization. "
            "Initializing with defaults from 'config.ini'.\n");
    initialize_global_task_config("config.ini");
  }
  return &g_task_config_instance;
}

void initialize_global_server_config(const char *filename) {
  if (g_server_config_initialized) {
    return;
  }

  if (load_server_config(filename, &g_server_config_instance) == 0) {
    printf("Global server configuration initialized successfully from '%s'.\n",
           filename);
  } else {
    printf("Failed to load global server configuration from '%s'. Using "
           "defaults.\n",
           filename);
  }
  g_server_config_initialized = 1;
}

const server_config_t *get_global_server_config(void) {
  if (!g_server_config_initialized) {
    fprintf(stderr,
            "Warning: Global server config accessed before initialization. "
            "Initializing with defaults from 'config.ini'.\n");
    initialize_global_server_config("config.ini");
  }
  return &g_server_config_instance;
}

#ifndef CONFIG_LOADER_H
#define CONFIG_LOADER_H

#include <stdint.h>

#define DEFAULT_ETH_DEV "eth0"
#define DEFAULT_MTU 1500
#define DEFAULT_PROTOCOL 0x88B5
#define DEFAULT_LOCAL_ID 1
#define DEFAULT_REMOTE_ID 2
#define DEFAULT_LOCAL_PORT 12345
#define DEFAULT_REMOTE_PORT 54321
#define DEFAULT_SITP_Q_CAP 1024  // Default SITP queue capacity
#define DEFAULT_SEND_DELAY 0     // Default delay in microseconds (0 = no delay)
#define DEFAULT_PKT_TRIM_BYTES 0 // Default packet trim bytes
#define MAX_ETH_DEV_LEN 32
#define MAX_IP_LEN 40 // Max length for an IPv6 address string + null terminator

#define DEFAULT_MAIN_LISTEN_PORT 9999 // Default port for the main server
#define DEFAULT_MAIN_LISTEN_IP "0.0.0.0"
#define DEFAULT_LOG_TO_FILE 0
#define DEFAULT_LOG_FILENAME "tun_server.log"
#define MAX_LOG_FILENAME_LEN 256
#define DEFAULT_LOG_LEVEL LOG_TRACE
#define DEFAULT_IS_MASTER 0 // 0 = not master

typedef struct {
  char eth_dev[MAX_ETH_DEV_LEN];
  int mtu;
  uint16_t protocol;
  uint16_t local_id;
  uint16_t remote_id;
  uint16_t local_port;
  uint16_t remote_port;
  int sitp_q_cap;              // SITP queue capacity
  unsigned int send_delay;     // Delay after sending a packet, in microseconds
  unsigned int pkt_trim_bytes; // Number of bytes to trim from packets
} task_config_t;

typedef struct {
  uint16_t listen_port;
  char listen_ip[MAX_IP_LEN]; // IP address to listen on
  int log_to_file;
  char log_filename[MAX_LOG_FILENAME_LEN];
  int log_level;
} server_config_t;

// The global configuration instances are now static to config_loader.c
// Use the accessor functions to get the configuration.

/*
 * Loads configuration for the SIPT task from the specified INI file.
 * If the file cannot be read or some parameters are missing,
 * default values will be used for those parameters.
 *
 * @param filename The path to the INI configuration file.
 * @param config Pointer to the task_config_t struct to be filled.
 * @return 0 on success, -1 on file open error (defaults will still be set).
 */
int load_task_config(const char *filename,
                     task_config_t *config); // Remains for direct use if
                                             // needed, but global is preferred

/**
 * @brief Initializes the global task configuration by loading it from the
 * specified file. This function should be called once at the application
 * startup.
 * @param filename The path to the INI configuration file.
 */
void initialize_global_task_config(const char *filename);

/**
 * @brief Retrieves a pointer to the globally loaded task configuration.
 * @return const task_config_t* Pointer to the global task configuration struct.
 */
const task_config_t *get_global_task_config(void);

/*
 * Loads configuration for the main server from the specified INI file.
 * If the file cannot be read or some parameters are missing,
 * default values will be used for those parameters.
 *
 * @param filename The path to the INI configuration file.
 * @param config Pointer to the server_config_t struct to be filled.
 * @return 0 on success, -1 on file open error (defaults will still be set).
 */
int load_server_config(
    const char *filename,
    server_config_t *config); // Remains for direct use if needed

/**
 * @brief Initializes the global server configuration by loading it from the
 * specified file. This function should be called once at the application
 * startup.
 * @param filename The path to the INI configuration file.
 */
void initialize_global_server_config(const char *filename);

/**
 * @brief Retrieves a pointer to the globally loaded server configuration.
 * @return const server_config_t* Pointer to the global server configuration
 * struct.
 */
const server_config_t *get_global_server_config(void);

#endif /* CONFIG_LOADER_H */

#include "tcp_client.h"
#include "ut/uthash.h"
#include <arpa/inet.h>
#include <event2/buffer.h>
#include <event2/bufferevent.h>
#include <event2/event.h>
#include <netinet/in.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <unistd.h>

// Client instance structure
struct tcp_client {
  char target_ip[40];            // Target IP address
  unsigned short target_port;    // Target port
  struct event_base *event_base; // libevent base
  struct bufferevent *bev;       // Buffer event for the connection
  int is_connected;              // Connection status flag
  int is_connecting;             // Connection in progress flag
  struct event *reconnect_event; // Event for reconnection timer
  int is_initialized;     // Flag indicating if the client is fully initialized
  pthread_t event_thread; // Event loop thread
  int should_cleanup;     // Flag to indicate if cleanup is in progress
  pthread_mutex_t mutex;  // Mutex for thread safety
};

// Forward declarations
static void tcp_client_event_cb(struct bufferevent *bev, short events,
                                void *ctx);
static void tcp_client_read_cb(struct bufferevent *bev, void *ctx);
static void reconnect_timer_cb(evutil_socket_t fd, short events, void *arg);
static int start_connection(tcp_client_t *client);
static int ensure_tcp_client_initialized(tcp_client_t *client);
static void *event_loop_thread(void *arg);

// Global TCP client hashmap entry structure
typedef struct global_tcp_client_entry {
  char target_ip[40];   // Key: target IP address
  tcp_client_t *client; // TCP client instance
  UT_hash_handle hh;    // uthash handle
} global_tcp_client_entry_t;

// Global TCP client hashmap and mutex
static global_tcp_client_entry_t *g_global_tcp_clients = NULL;
static pthread_mutex_t g_global_clients_mutex = PTHREAD_MUTEX_INITIALIZER;

/**
 * Initialize a new TCP client instance with the specified target IP and port.
 *
 * @param target_ip The target IP address
 * @param target_port The target port
 * @return Pointer to the client instance on success, NULL on failure
 */
tcp_client_t *tcp_client_init(const char *target_ip,
                              unsigned short target_port) {
  tcp_client_t *client = malloc(sizeof(tcp_client_t));
  if (!client) {
    fprintf(stderr, "Failed to allocate memory for TCP client\n");
    return NULL;
  }

  // Initialize the structure
  memset(client, 0, sizeof(tcp_client_t));

  // Store the target IP and port
  strncpy(client->target_ip, target_ip, sizeof(client->target_ip) - 1);
  client->target_ip[sizeof(client->target_ip) - 1] =
      '\0'; // Ensure null termination
  client->target_port = target_port;

  // Initialize mutex
  if (pthread_mutex_init(&client->mutex, NULL) != 0) {
    fprintf(stderr, "Failed to initialize mutex\n");
    free(client);
    return NULL;
  }

  printf("TCP client instance created: target IP: %s, port: %u\n",
         client->target_ip, client->target_port);

  ensure_tcp_client_initialized(client);

  return client;
}

/**
 * Ensure the TCP client is fully initialized.
 * This function performs the actual initialization if it hasn't been done yet.
 *
 * @param client Pointer to the client instance
 * @return 0 on success, non-zero on failure
 */
static int ensure_tcp_client_initialized(tcp_client_t *client) {
  if (!client) {
    return -1;
  }

  pthread_mutex_lock(&client->mutex);

  // If already initialized, return success
  if (client->is_initialized) {
    pthread_mutex_unlock(&client->mutex);
    return 0;
  }

  // Initialize libevent
  client->event_base = event_base_new();
  if (!client->event_base) {
    fprintf(stderr, "Could not initialize libevent for TCP client\n");
    pthread_mutex_unlock(&client->mutex);
    return -1;
  }

  // Create a reconnect timer event
  client->reconnect_event =
      event_new(client->event_base, -1, EV_PERSIST, reconnect_timer_cb, client);
  if (!client->reconnect_event) {
    fprintf(stderr, "Could not create reconnect timer event\n");
    event_base_free(client->event_base);
    client->event_base = NULL;
    pthread_mutex_unlock(&client->mutex);
    return -1;
  }

  // Start the initial connection
  if (start_connection(client) != 0) {
    fprintf(stderr, "Failed to start initial connection\n");
    // We'll continue and let the reconnect timer handle it
  }

  // Start the event loop in a separate thread
  if (pthread_create(&client->event_thread, NULL, event_loop_thread, client) !=
      0) {
    fprintf(stderr, "Failed to create event thread\n");
    event_free(client->reconnect_event);
    client->reconnect_event = NULL;
    event_base_free(client->event_base);
    client->event_base = NULL;
    pthread_mutex_unlock(&client->mutex);
    return -1;
  }

  client->is_initialized = 1;
  printf(
      "TCP client fully initialized and connected to target IP: %s, port: %u\n",
      client->target_ip, client->target_port);

  pthread_mutex_unlock(&client->mutex);
  return 0;
}

/**
 * Event loop thread function.
 *
 * @param arg Pointer to the client instance
 * @return NULL
 */
static void *event_loop_thread(void *arg) {
  tcp_client_t *client = (tcp_client_t *)arg;

  // Run the event loop
  event_base_dispatch(client->event_base);

  return NULL;
}

/**
 * Clean up resources used by the TCP client instance.
 *
 * @param client Pointer to the client instance
 */
void tcp_client_cleanup(tcp_client_t *client) {
  if (!client) {
    return;
  }

  pthread_mutex_lock(&client->mutex);
  client->should_cleanup = 1;

  if (client->event_base) {
    // Stop the event loop
    event_base_loopbreak(client->event_base);
    pthread_mutex_unlock(&client->mutex);

    // Wait for the event thread to finish
    if (client->is_initialized) {
      pthread_join(client->event_thread, NULL);
    }

    pthread_mutex_lock(&client->mutex);

    // Clean up the reconnect timer
    if (client->reconnect_event) {
      event_free(client->reconnect_event);
      client->reconnect_event = NULL;
    }

    // Clean up the bufferevent
    if (client->bev) {
      bufferevent_free(client->bev);
      client->bev = NULL;
    }

    // Clean up the event base
    event_base_free(client->event_base);
    client->event_base = NULL;
  }

  client->is_connected = 0;
  client->is_connecting = 0;
  client->is_initialized = 0;

  pthread_mutex_unlock(&client->mutex);
  pthread_mutex_destroy(&client->mutex);

  printf("TCP client cleaned up\n");

  free(client);
}

/**
 * Send data to the target server.
 *
 * @param client Pointer to the client instance
 * @param data Pointer to the data buffer
 * @param len Length of the data
 * @return 0 on success, non-zero on failure
 */
int tcp_client_send(tcp_client_t *client, const void *data, size_t len) {
  if (!client) {
    return -1;
  }

  // Ensure the TCP client is initialized
  if (!client->is_initialized) {
    if (ensure_tcp_client_initialized(client) != 0) {
      fprintf(stderr, "Failed to initialize TCP client\n");
      return -1;
    }
  }

  pthread_mutex_lock(&client->mutex);

  if (client->should_cleanup) {
    pthread_mutex_unlock(&client->mutex);
    return -1;
  }

  if (!client->is_connected || !client->bev) {
    fprintf(stderr, "Cannot send data: not connected\n");
    pthread_mutex_unlock(&client->mutex);
    return -1;
  }

  if (bufferevent_write(client->bev, data, len) != 0) {
    fprintf(stderr, "Failed to write data to buffer\n");
    pthread_mutex_unlock(&client->mutex);
    return -1;
  }

  pthread_mutex_unlock(&client->mutex);
  return 0;
}

/**
 * Start a connection to the target server.
 *
 * @param client Pointer to the client instance
 * @return 0 on success, non-zero on failure
 */
static int start_connection(tcp_client_t *client) {
  if (!client || client->is_connecting) {
    return 0; // Connection attempt already in progress
  }

  // Clean up any existing connection
  if (client->bev) {
    bufferevent_free(client->bev);
    client->bev = NULL;
  }

  client->is_connected = 0;
  client->is_connecting = 1;

  // Create a new bufferevent
  client->bev =
      bufferevent_socket_new(client->event_base, -1, BEV_OPT_CLOSE_ON_FREE);
  if (!client->bev) {
    fprintf(stderr, "Could not create bufferevent\n");
    client->is_connecting = 0;
    return -1;
  }

  // Set up callbacks
  bufferevent_setcb(client->bev, tcp_client_read_cb, NULL, tcp_client_event_cb,
                    client);
  bufferevent_enable(client->bev, EV_READ | EV_WRITE);

  // Set up the target address
  struct sockaddr_in sin;
  memset(&sin, 0, sizeof(sin));
  sin.sin_family = AF_INET;
  sin.sin_addr.s_addr = inet_addr(client->target_ip);
  sin.sin_port = htons(client->target_port);

  // Start the connection
  if (bufferevent_socket_connect(client->bev, (struct sockaddr *)&sin,
                                 sizeof(sin)) < 0) {
    fprintf(stderr, "Failed to connect to %s:%u\n", client->target_ip,
            client->target_port);
    bufferevent_free(client->bev);
    client->bev = NULL;
    client->is_connecting = 0;

    // Schedule a reconnection attempt
    struct timeval tv = {5, 0}; // 5 seconds
    event_add(client->reconnect_event, &tv);

    return -1;
  }

  return 0;
}

/**
 * Callback for bufferevent events.
 */
static void tcp_client_event_cb(struct bufferevent *bev, short events,
                                void *ctx) {
  tcp_client_t *client = (tcp_client_t *)ctx;

  if (!client) {
    return;
  }

  pthread_mutex_lock(&client->mutex);

  if (client->should_cleanup) {
    pthread_mutex_unlock(&client->mutex);
    return;
  }

  if (events & BEV_EVENT_CONNECTED) {
    // Connection established
    client->is_connected = 1;
    client->is_connecting = 0;
    event_del(client->reconnect_event); // Stop the reconnect timer
    printf("Connected to %s:%u\n", client->target_ip, client->target_port);
  } else if (events & (BEV_EVENT_ERROR | BEV_EVENT_EOF)) {
    // Connection closed or error
    if (events & BEV_EVENT_ERROR) {
      fprintf(stderr, "Connection error: %s\n",
              evutil_socket_error_to_string(EVUTIL_SOCKET_ERROR()));
    } else {
      fprintf(stderr, "Connection closed by remote host\n");
    }

    bufferevent_free(client->bev);
    client->bev = NULL;
    client->is_connected = 0;
    client->is_connecting = 0;

    // Schedule a reconnection attempt
    struct timeval tv = {5, 0}; // 5 seconds
    event_add(client->reconnect_event, &tv);
  }

  pthread_mutex_unlock(&client->mutex);
}

/**
 * Callback for read events.
 */
static void tcp_client_read_cb(struct bufferevent *bev, void *ctx) {
  tcp_client_t *client = (tcp_client_t *)ctx;

  if (!client) {
    return;
  }

  // We don't expect to receive data in this implementation, but we'll handle it
  // anyway
  struct evbuffer *input = bufferevent_get_input(bev);
  size_t len = evbuffer_get_length(input);

  if (len > 0) {
    // Just drain the buffer
    evbuffer_drain(input, len);
    printf("Received %zu bytes from server (discarded)\n", len);
  }
}

/**
 * Callback for the reconnection timer.
 */
static void reconnect_timer_cb(evutil_socket_t fd, short events, void *arg) {
  tcp_client_t *client = (tcp_client_t *)arg;

  if (!client) {
    return;
  }

  pthread_mutex_lock(&client->mutex);

  if (client->should_cleanup) {
    pthread_mutex_unlock(&client->mutex);
    return;
  }

  printf("Attempting to reconnect to %s:%u\n", client->target_ip,
         client->target_port);
  start_connection(client);

  pthread_mutex_unlock(&client->mutex);
}

/**
 * Initialize a global TCP client with the specified target IP and port.
 * If a client for this IP already exists, it will be replaced.
 *
 * @param target_ip The target IP address
 * @param target_port The target port
 * @return 0 on success, non-zero on failure
 */
int init_global_tcp_client(const char *target_ip, unsigned short target_port) {
  if (!target_ip) {
    fprintf(stderr, "Invalid target IP address\n");
    return -1;
  }

  pthread_mutex_lock(&g_global_clients_mutex);

  // Check if client for this IP already exists
  global_tcp_client_entry_t *existing_entry;
  HASH_FIND_STR(g_global_tcp_clients, target_ip, existing_entry);

  // Clean up existing client if any
  if (existing_entry) {
    tcp_client_cleanup(existing_entry->client);
    HASH_DEL(g_global_tcp_clients, existing_entry);
    free(existing_entry);
  }

  // Create new client entry
  global_tcp_client_entry_t *new_entry =
      malloc(sizeof(global_tcp_client_entry_t));
  if (!new_entry) {
    fprintf(stderr, "Failed to allocate memory for TCP client entry\n");
    pthread_mutex_unlock(&g_global_clients_mutex);
    return -1;
  }

  // Initialize the entry
  strncpy(new_entry->target_ip, target_ip, sizeof(new_entry->target_ip) - 1);
  new_entry->target_ip[sizeof(new_entry->target_ip) - 1] = '\0';

  // Create new TCP client
  new_entry->client = tcp_client_init(target_ip, target_port);
  if (!new_entry->client) {
    fprintf(stderr, "Failed to initialize TCP client for IP: %s\n", target_ip);
    free(new_entry);
    pthread_mutex_unlock(&g_global_clients_mutex);
    return -1;
  }

  // Add to hashmap
  HASH_ADD_STR(g_global_tcp_clients, target_ip, new_entry);

  pthread_mutex_unlock(&g_global_clients_mutex);

  printf("Global TCP client initialized successfully for IP: %s, port: %u\n",
         target_ip, target_port);
  return 0;
}

/**
 * Get the global TCP client instance for the specified IP.
 *
 * @param target_ip The target IP address
 * @return Pointer to the client instance, NULL if not found
 */
tcp_client_t *get_global_tcp_client(const char *target_ip) {
  if (!target_ip) {
    return NULL;
  }

  pthread_mutex_lock(&g_global_clients_mutex);

  global_tcp_client_entry_t *entry;
  HASH_FIND_STR(g_global_tcp_clients, target_ip, entry);

  tcp_client_t *client = entry ? entry->client : NULL;

  pthread_mutex_unlock(&g_global_clients_mutex);

  return client;
}

/**
 * Send data using the global TCP client for the specified IP.
 *
 * @param target_ip The target IP address
 * @param data Pointer to the data buffer
 * @param len Length of the data
 * @return 0 on success, non-zero on failure
 */
int global_tcp_client_send(const char *target_ip, const void *data,
                           size_t len) {
  if (!target_ip) {
    fprintf(stderr, "Invalid target IP address\n");
    return -1;
  }

  pthread_mutex_lock(&g_global_clients_mutex);

  global_tcp_client_entry_t *entry;
  HASH_FIND_STR(g_global_tcp_clients, target_ip, entry);

  if (!entry || !entry->client) {
    fprintf(stderr, "Global TCP client not found for IP: %s\n", target_ip);
    pthread_mutex_unlock(&g_global_clients_mutex);
    return -1;
  }

  tcp_client_t *client = entry->client;
  pthread_mutex_unlock(&g_global_clients_mutex);

  return tcp_client_send(client, data, len);
}

/**
 * Clean up the global TCP client for the specified IP.
 *
 * @param target_ip The target IP address, NULL to clean up all clients
 */
void cleanup_global_tcp_client(const char *target_ip) {
  pthread_mutex_lock(&g_global_clients_mutex);

  if (target_ip) {
    // Clean up specific client
    global_tcp_client_entry_t *entry;
    HASH_FIND_STR(g_global_tcp_clients, target_ip, entry);

    if (entry) {
      tcp_client_cleanup(entry->client);
      HASH_DEL(g_global_tcp_clients, entry);
      free(entry);
      printf("Global TCP client cleaned up for IP: %s\n", target_ip);
    } else {
      printf("No global TCP client found for IP: %s\n", target_ip);
    }
  } else {
    // Clean up all clients
    global_tcp_client_entry_t *entry, *tmp;
    HASH_ITER(hh, g_global_tcp_clients, entry, tmp) {
      tcp_client_cleanup(entry->client);
      HASH_DEL(g_global_tcp_clients, entry);
      free(entry);
    }
    printf("All global TCP clients cleaned up\n");
  }

  pthread_mutex_unlock(&g_global_clients_mutex);
}

/**
 * Get the count of global TCP clients.
 *
 * @return Number of global TCP clients
 */
unsigned int get_global_tcp_client_count(void) {
  pthread_mutex_lock(&g_global_clients_mutex);
  unsigned int count = HASH_COUNT(g_global_tcp_clients);
  pthread_mutex_unlock(&g_global_clients_mutex);
  return count;
}

/**
 * List all global TCP client IPs.
 *
 * @param ips Array to store IP addresses (caller must provide sufficient space)
 * @param max_count Maximum number of IPs to return
 * @return Number of IPs returned
 */
unsigned int list_global_tcp_client_ips(char ips[][40],
                                        unsigned int max_count) {
  pthread_mutex_lock(&g_global_clients_mutex);

  global_tcp_client_entry_t *entry;
  unsigned int count = 0;

  for (entry = g_global_tcp_clients; entry != NULL && count < max_count;
       entry = entry->hh.next) {
    strncpy(ips[count], entry->target_ip, 39);
    ips[count][39] = '\0';
    count++;
  }

  pthread_mutex_unlock(&g_global_clients_mutex);
  return count;
}

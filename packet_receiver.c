#include "packet_receiver.h"
#include "send_utils.h"
#include "message_common.h"
#include <event2/buffer.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define HEADER_SIZE sizeof(message_header_t)
#define MAX_PACKET_SIZE 65535

packet_receiver_context_t *packet_receiver_init(void) {
  packet_receiver_context_t *ctx =
      (packet_receiver_context_t *)malloc(sizeof(packet_receiver_context_t));
  if (ctx == NULL) {
    return NULL;
  }

  ctx->state = STATE_WAITING_HEADER;
  ctx->header_bytes_received = 0;
  ctx->data_buffer = NULL;
  ctx->data_length = 0;
  ctx->data_bytes_received = 0;

  return ctx;
}

void packet_receiver_cleanup(packet_receiver_context_t *ctx) {
  if (ctx == NULL) {
    return;
  }

  if (ctx->data_buffer != NULL) {
    free(ctx->data_buffer);
  }

  free(ctx);
}

int packet_receiver_process_data(struct bufferevent *bev,
                                 packet_receiver_context_t *ctx) {
  struct evbuffer *input = bufferevent_get_input(bev);
  size_t available = evbuffer_get_length(input);

  while (available > 0) {
    if (ctx->state == STATE_WAITING_HEADER) {
      size_t bytes_needed = HEADER_SIZE - ctx->header_bytes_received;

      size_t bytes_to_read =
          (available < bytes_needed) ? available : bytes_needed;

      evbuffer_remove(input, ctx->header_buffer + ctx->header_bytes_received,
                      bytes_to_read);
      ctx->header_bytes_received += bytes_to_read;
      available -= bytes_to_read;

      if (ctx->header_bytes_received == HEADER_SIZE) {
        message_header_t *header = (message_header_t *)ctx->header_buffer;

        if (memcmp(header->magic, MAGIC_NUMBER, MAGIC_LEN) != 0) {
          fprintf(stderr, "Invalid magic string in packet header\n");
          ctx->state = STATE_WAITING_HEADER;
          ctx->header_bytes_received = 0;
          continue;
        }

        if (header->type != MSG_TYPE_DATA_SYNC) {
          fprintf(stderr, "Invalid message type: %d, expected DATA_SYNC (%d)\n",
                  header->type, MSG_TYPE_DATA_SYNC);
          ctx->state = STATE_WAITING_HEADER;
          ctx->header_bytes_received = 0;
          continue;
        }

        ctx->data_length = header->len;

        if (ctx->data_length < 0 || ctx->data_length > MAX_PACKET_SIZE) {
          fprintf(stderr, "Invalid packet length: %d\n", ctx->data_length);
          ctx->state = STATE_WAITING_HEADER;
          ctx->header_bytes_received = 0;
          continue;
        }

        printf("Received valid DATA_SYNC message header, payload length: %d\n", ctx->data_length);

        if (ctx->data_length > 0) {
          ctx->data_buffer = (uint8_t *)malloc(ctx->data_length);
          if (ctx->data_buffer == NULL) {
            fprintf(stderr, "Failed to allocate memory for packet data\n");
            ctx->state = STATE_WAITING_HEADER;
            ctx->header_bytes_received = 0;
            return -1;
          }
        } else {
          ctx->data_buffer = NULL;
        }

        ctx->data_bytes_received = 0;
        ctx->state = STATE_WAITING_DATA;
      }
    } else if (ctx->state == STATE_WAITING_DATA) {
      if (ctx->data_length == 0) {
        printf("Received complete packet with no payload\n");

        int result = process_and_send_packet(NULL, 0);
        if (result != 0) {
          fprintf(stderr, "Failed to process and send packet\n");
        }

        ctx->state = STATE_WAITING_HEADER;
        ctx->header_bytes_received = 0;
        continue;
      }

      size_t bytes_needed = ctx->data_length - ctx->data_bytes_received;

      size_t bytes_to_read =
          (available < bytes_needed) ? available : bytes_needed;

      evbuffer_remove(input, ctx->data_buffer + ctx->data_bytes_received,
                      bytes_to_read);
      ctx->data_bytes_received += bytes_to_read;
      available -= bytes_to_read;

      if (ctx->data_bytes_received == ctx->data_length) {
        printf("Received complete packet of %d bytes\n", ctx->data_length);

        int result =
            process_and_send_packet(ctx->data_buffer, ctx->data_length);
        if (result != 0) {
          fprintf(stderr, "Failed to process and send packet\n");
        }

        if (ctx->data_buffer != NULL) {
          free(ctx->data_buffer);
          ctx->data_buffer = NULL;
        }

        ctx->state = STATE_WAITING_HEADER;
        ctx->header_bytes_received = 0;
      }
    }
  }

  return 0;
}
